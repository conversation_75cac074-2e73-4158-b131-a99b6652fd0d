# What's New

## Version 0.0.70
- Fix Selection Clear

## Version 0.0.69
- Fix：close auto resolve conflict
- Fix：toco version file was treated as binary 

## Version 0.0.68
- Fix: fossil hard rollback
- Fix: read class in jar when multiple found
- Feature: add update plugin tip when receiving unsupported bridge

## Version 0.0.67
- Feature: add removeFiles bridge

## Version 0.0.66
- Fix: findInFiles return relative path
- Feature: readFiles return file ext

## Version 0.0.65
- Feature: add mastery for auto resolve conflict

## Version 0.0.64
- Remove: IntelliMerge, not flexible and not stable
- Fix: Resolve conflict by accept all code from local when first time module code generate when code exist

## Version 0.0.63
- Fix: Initial Fossil project indicator
- Fix: Open Fossil ui block ui-thread

## Version 0.0.62
- Fix: Fossil add command blocked by prompt

## Version 0.0.61
- Remove: stop code locator service temporarily(to save memory avoid cef crash).

## Version 0.0.60
- Fix: linux browser IME not usable(set OSR to true)
- Fix: none OSR browser multiscreen position

## Version 0.0.59
- Fix: Empty directory detect(case fossil update)
- Improve: Maven info cache logic, save mass of memory

## Version 0.0.58
- Fix: Selection support all files

## Version 0.0.57
- Fix: Linux open devTool button of Chat

## Version 0.0.56
- Feature: _support idea 2025.2_

## Version 0.0.55
- Improve: New Toco project select JDK
- Improve: New Toco groupId replace '-' to '_'
- Fix: TocoBrowser tab hide split menu item
- Fix: Add IntelliMerge log

## Version 0.0.54
- Improve: Set offline-screen-rendering false when create chat webview

## Version 0.0.53
- Fix: findInFiles bug: could not search substring in method
- Feature: support regex search in findInFiles

## Version 0.0.52
- Feature: Modeling apply
- Improve: Toggle GC before and after readFiles/findJavaClass/findInFile
- Improve: Set offline-screen-rendering true when create chat webview

## Version 0.0.51
- Feature: add IntelliMerge Debug 

## Version 0.0.50
- Feature: add IntelliMerge

## Version 0.0.49
- Feature: add compile specified files tool to enhance buildProject

## Version 0.0.48
- Feature: support open file
- Feature: support delete file when discard new created file
- Feature: support right side diff editable

## Version 0.0.47
- Feature：enhance apply diff in editFile tool
- Feature：optimize error message when apply diff fail 

## Version 0.0.46
- Feature：change data structure of editFile result
- Fix：validate origin line numbers in hunk header with origin file

## Version 0.0.45
- Fix: auto fix unified diff new line count calc error

## Version 0.0.44
- Feature: buildProject remove warnings
- Feature: add findJavaClasses tool

## Version 0.0.43
- Feature: listFiles only return java file list

## Version 0.0.42
- Feature: add findInFiles bridge

## Version 0.0.41
- Feature: add listOpenedFiles bridge

## Version 0.0.40
- Feature: add addLog bridge

## Version 0.0.39
- Feature: support discardAll

## Version 0.0.38
- Feature: editFile tool support create file directly
- Feature: add logger for editFile tool

## Version 0.0.37
- Fix: fix read file content issue from FileIO
- Fix：tab not active in showInlineDiff

## Version 0.0.36
- Feature: add readFiles tool to read multiple files

## Version 0.0.35
- Feature: findJavaClass tool return class content when single found, otherwise return class path list if multiple found
- Feature: findJavaClass and readFile tool support reading class file in jar

## Version 0.0.34
- Feature: show diff highlight in changed file editor
- Feature: add accept and acceptAll bridge for changed files
- Fix：close-tab EDT exception

## Version 0.0.33
- Fix: Send system language to frontend
- Feature: Use inline diff when edit file

## Version 0.0.32
- Improve: Toggle GC before and after code generation or edit file
- Fix: WebviewEditor background color when change theme

## Version 0.0.31
- Fix: crashes on IDEA 2025.1.3
- Remove: codebase related

## Version 0.0.30
- Feature: modify createFile、editFile tool return result
- Feature：editFile add patch error message

## Version 0.0.29
- Fix: fix editFile tool robustness 

## Version 0.0.28
- Fix: dependence type field deleted when merging poms
- Fix: webview prevent remove message router
- Fix: After file monitoring, wait for the smart mode to execute subsequent logic

## Version 0.0.27
- Feature: Add tools findJavaClass, buildProject
- Fix: Fix codebase search outdated stub index error
- Fix: Add non-toco project detection on project close
- Fix: Fix locate code error

## Version 0.0.26
- Feature: Add parent class content in readFile tool
- Fix: non-osr webview could not open in Idea 2025.1

## Version 0.0.25
- Feature: Add frontend host config for debug in Application Settings
- Feature: Add editFile failure log when patching code

## Version 0.0.24
- Fix: Ignored file preview use diff viewer instead of merge viewer
- Fix: Webview not managed by WebViewManager when project is null
- Fix: Right Toco tool window drag problem
- Improve: Ignored file recovery modal do not show again option

## Version 0.0.23
- Fix: Webview flash white problem
- Fix: Login webview log cursor
- Fix: Toco element locate
- Fix: Prevent plugin activity from project without toco
- Fix: Generate code progress indicator hung
- Fix: Generate code failed after delete module physically
- Fix: Fossil error after fossil file migration
- Fix: Login page problem in offline web mode
- Feature: Toco tab loading state and reload shortcut
- Feature: Toco page theme
- Feature: Ignored file recovery
- Improve: Codebase search performance
- Remove: Maven reload

## Version 0.0.22
- Fix: none-offline-screen-rendering webview invisible on linux
- Fix: none-offline-screen-rendering webview devtools crash
- Fix: regenerate code after module deleted from disk

## Version 0.0.21
- In editFile tool, fix code merge bug
  1. repeat annotations in merged code
  2. comments and annotations in original code not removed

## Version 0.0.20
- Remove codebase search temporarily, due to performance issue

## Version 0.0.19
- readClassMethod tool support more code context (class, import, member variables)
- readFile tool support specify code line range
- in readFile and readClassMethod tool, try to fix VFS not consistent with disk
- add alert when closing toco tab with unsaved

## Version 0.0.18
- Add bridge for editFile tool

## Version 0.0.17
- Fix: projectId confuse, cased by WebViewManager singleton

## Version 0.0.16
- Revert GenerateIndicator changes: blcoks ui process
- Remove RAG file monitor：blcoks idea system file sync

## Version 0.0.15
- Improve fossil merge performace: remove cat file

## Version 0.0.14
- Add offline package
- Improve format code performace

## Version 0.0.13
- Add bridge for new tools (design_to_code, read_class_method)
- Fix drag file issue in chat window

## Version 0.0.12
- Fix tool window flash
- Fix store communicate via bridge
- Support for Apply All Code

## Version 0.0.11
- Add configService
- remove window.tocoPluginInfo

## Version 0.0.10
- Fix white screen issue
- support @ in aichat

## Version 0.0.9
- Fix tool window flash
- Fix store communicate via bridge
- Fix extension no such method exception

## Version 0.0.7
- Fix workspace cmd fail in win

## Version 0.0.6
- Support Apply Code

## Version 0.0.4
- Support Kotlin K2 mode
- Fix login/logout crash

## Version 0.0.2
- **Init:** alpha

