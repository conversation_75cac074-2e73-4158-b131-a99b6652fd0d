package com.think1024.tocodesign.ideaplugin.toco

import com.think1024.tocodesign.ideaplugin.utils.BinaryRunner
import com.think1024.tocodesign.ideaplugin.utils.BinaryRunner.Result
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.LightVirtualFile
import com.think1024.tocodesign.ideaplugin.mastery.driver.Config
import com.think1024.tocodesign.ideaplugin.mastery.driver.Driver
import com.think1024.tocodesign.ideaplugin.utils.FileUtil
import java.io.File
import java.nio.file.Paths

data class IgnoreConfig(
    val includePatterns: MutableList<String>,
    val excludeFiles: List<String>
)

data class Commit(
    val commitId: String? = null,
    val branch: String? = null,
    val comment: String? = null
)

enum class MergeStatus {
    ADDED,
    DELETED,
    RENAMED,
    UNMERGED,
    MODIFIED,
    IGNORED,
    UNCHANGED
}

enum class MergeResultStatus(val value: String) {
    Success("success"),
    Conflict("conflict"),
    Failed("failed"),
}

data class MergeFile(
    var type: MergeStatus,
    val oursPath: String,
    val oursContent: String,
    val theirsContent: String,
    val mergedPath: String,
    var mergedContent: String,
    val baseContent: String? = null,
    var conflictIgnored: Boolean = false,
    var conflictResolved: Boolean? = false,
)
fun MergeFile.getVirtualFile(project: Project?): VirtualFile {
    val virtualFile =
        if (this.type == MergeStatus.IGNORED) IgnoredVirtualFile(this, project)
        else FileUtil.getVirtualFile(this.mergedPath, project) ?: LightVirtualFile(this.mergedPath, null, this.mergedContent)
    return virtualFile
}

data class MergeResult(
    var status: MergeResultStatus,
    val files: List<MergeFile>? = null
)

data class MergeInfo(val status: String, val filePath: String)

data class ChangeInfo(
    val fromPath: String? = null,
    val toPath: String
)

data class BlameEntry(
    val commitId: String,
    val time: String,
    val user: String,
    val code: String
)

object FossilCmd {
    private val logger = Logger.getInstance(FossilCmd::class.java)
    private const val BINARY_NAME = "fossil"
    private const val BOT_NAME = "bot"
    private var uiProcess: Process? = null

    const val MODULE_FOLDER_NAME = "modules"

    fun getOpenMarkFile(): String {
        val isWindows = BinaryRunner.getPlatform() === "win32"
        if (isWindows) {
            return "_FOSSIL_"
        }
        return ".fslckout"
    }

    fun cleanDbConnectFile(projectWorkFolder: String) {
        // 部分情况下会删除fossil文件后重新恢复项目，如果工程项目之前存在.fslckout需要删掉否则无法open
        try {
            val markFile = File(Paths.get(projectWorkFolder, getOpenMarkFile()).toString())
            markFile.delete()
        } catch (_: Exception) {
            //
        }
        cleanDbJournalFile(projectWorkFolder)
    }

    fun cleanDbJournalFile(projectWorkFolder: String) {
        try {
            val markFile = FileUtil.getFile("${Paths.get(projectWorkFolder, getOpenMarkFile())}-journal")
            markFile?.delete()
        } catch (_: Exception) {
            //
        }
    }

    private fun isJavaFile(file: File): Boolean {
        return file.extension.lowercase() == "java"
    }

    fun isJava(filePath: String): Boolean {
        return isJavaFile(File(filePath))
    }

    private fun isFileInDirectory(filePath: String, directoryPath: String): Boolean {
        // 将路径转换为 File 对象
        val file = File(filePath).absoluteFile
        val directory = File(directoryPath).absoluteFile

        // 检查目录是否有效
        if (!directory.exists() || !directory.isDirectory) {
            return false
        }

        // 检查文件路径是否以目录路径为前缀
        val pathMatch = file.canonicalPath.startsWith(directory.canonicalPath + File.separator)
        if (!pathMatch) {
            return false
        }
        return file.exists()
    }

    private fun parseGitignore(gitignorePath: String): IgnoreConfig {
        val includePatterns = mutableSetOf(
            ".git/*",
            ".mvn/*",
            "mvnw",
            "mvnw.cmd",
            "Thumbs.db",
            "*/Thumbs.db",
            ".DS_Store",
            "*/.DS_Store",
            ".gitkeep",
            "*/.gitkeep"
        )
        val excludeFiles = mutableSetOf<String>()

        try {
            val ignoreFile = FileUtil.getFile(gitignorePath)
            if (ignoreFile != null) {
                val content = ignoreFile.readText()
                val lines = content.split("\n")
                    .map { it.trim() }
                    .filter { it.isNotEmpty() }

                for (line in lines) {
                    if (line.isEmpty() || line.startsWith("#")) {
                        continue // 跳过空行和注释
                    }
                    if (line.startsWith("!")) {
                        // 否定模式，记录为排除文件
                        val file = line.substring(1).replace(Regex("^/+"), "") // 去掉 ! 和前导 /
                        excludeFiles.add(file)
                    } else {
                        // 包含模式，转换为 Fossil 的 glob
                        var pattern = line.replace(Regex("^/+"), "") // 去掉前导 /
                        when {
                            pattern.endsWith("/**") -> {
                                // 转换 a/** 或 ** 为 "/*"
                                pattern = pattern.replace("/**", "/*")
                                includePatterns.add("*/${pattern}")
                            }
                            pattern.endsWith("/") -> {
                                // 转换目录 a/ 为 a/*
                                pattern = "${pattern}*"
                                includePatterns.add("*/${pattern}")
                            }
                        }
                        includePatterns.add(pattern)
                    }
                }
            }
        } catch (e: Exception) {
            logger.error("[ParseGitIgnore]: ${e.message}")
        }

        return IgnoreConfig(
            includePatterns = includePatterns.toMutableList(),
            excludeFiles = excludeFiles.toList()
        )
    }

    private fun getIgnore(workingFolder: String, customIgnores: List<String>? = null): String {
        val ignoreConfig = parseGitignore(Paths.get(workingFolder, ".gitignore").toString())
        ignoreConfig.includePatterns.addAll(customIgnores ?: emptyList())
        return ignoreConfig.includePatterns.joinToString(",")
    }

    private fun fossil(vararg args: String, cwd: String = ""): Result {
        val path = BinaryRunner.getBinaryPath(BINARY_NAME)
        val ret = BinaryRunner.runSync(path.toString(), *args, "--user", BOT_NAME, cwd = cwd)
        if (!ret.error.isNullOrEmpty()) {
            val command = args[0]
            logger.warn("[fossil $command] ${ret.error}")
        }
        return ret
    }

    fun openRepoUI(workingFolder: String) {
        closeRepoUI()
        val path = BinaryRunner.getBinaryPath(BINARY_NAME)
        uiProcess = BinaryRunner.runCommand(
            path.toString(),
            "ui", "-p", "timeline",
            cwd = workingFolder,
            onError = { err -> logger.error(err) }
        )
    }

    fun closeRepoUI() {
        if (uiProcess?.isAlive == true) {
            uiProcess?.destroyForcibly()
        }
    }

    fun fixFossilAdmin(workingFolder: String) {
         try {
             val res = fossil("sql", "SELECT login FROM user WHERE cap LIKE '%s%';", cwd = workingFolder)
             if (res.error.isNullOrEmpty()) {
                 if (res.output.removeSurrounding("'") != BOT_NAME) {
                     fossil("sql", "UPDATE user SET login = '$BOT_NAME' WHERE cap = 's';", cwd = workingFolder)
                 }
             }
        } catch (e: Exception) {
             logger.error("[fossil fixFossilAdmin]: ${e.message}")
        }
    }

    fun createRepo(projectName: String, projectsFolder: String): String? {
        val dir = Paths.get(projectsFolder, projectName).toString()
        try {
            File(dir).mkdirs()
        } catch (e: Exception) {
            logger.error("[fossil createRepo]: ${e.message}")
            return null
        }

        val fileName = "$projectName.fossil"
        val projectFilePath = Paths.get(dir, fileName).toString()
        val projectFile = FileUtil.getFile(projectFilePath)
        if (projectFile != null) {
            return projectFilePath
        }

        try {
            fossil("init", fileName, "--project-name", projectName, "-A", BOT_NAME, cwd = dir)
        } catch (e: Exception) {
            logger.error("[fossil createRepo]: ${e.message}")
            return null
        }

        return projectFilePath
    }

    /**
     * 打开Fossil仓库
     */
    fun openRepo(projectFile: String, workingFolder: String): Boolean {
        // 检查是否已经open
        var opened = false
        val state = status(workingFolder)
        if (state.isNotEmpty()) {
            opened = true
        }

        // open
        if (!opened) {
            try {
                val res = fossil("open", projectFile, "-k", cwd = workingFolder)
                if (!res.error.isNullOrEmpty()) {
                    // fossil的数据库连接文件有问题，需要删除重开
                    cleanDbConnectFile(workingFolder)
                    fossil("open", projectFile, "-k", cwd = workingFolder)
                }
            } catch (e: Exception) {
                logger.error("[fossil openRepo]: ${e.message}")
                return false
            }
        }
        return true
    }

    fun status(workingFolder: String): String {
        return try {
            val ret = fossil("status", cwd = workingFolder)
            ret.output
        } catch (e: Exception) {
            logger.error("[fossil status]: ${e.message}")
            ""
        }
    }

    fun closeRepo(projectFile: String, workingFolder: String): Boolean {
        return try {
            val ret = fossil("close", projectFile, cwd = workingFolder)
            ret.error.isNullOrEmpty()
        } catch (e: Exception) {
            logger.error("[fossil closeRepo]: ${e.message}")
            false
        }
    }

    fun updateRepo(workingFolder: String, commitId: String? = null, vararg files: String): Boolean {
        return try {
            when(commitId) {
                null -> fossil("update", *files, cwd = workingFolder)
                else -> fossil("update", commitId, *files, cwd = workingFolder)
            }
            true
        } catch (e: Exception) {
            logger.error("[fossil updateRepo]: ${e.message}")
            false
        }
    }

    fun cloneRepo(projectFile: String, workingFolder: String): Boolean {
        return try {
            val ret = fossil("clone", projectFile, cwd = workingFolder)
            ret.error.isNullOrEmpty()
        } catch (e: Exception) {
            logger.error("[fossil cloneRepo]: ${e.message}")
            false
        }
    }

    fun isRepoClean(workingFolder: String): Boolean {
        return try {
            val ignore = getIgnore(workingFolder)
            val changes = fossil("changes", cwd = workingFolder)
            val extraArgs = if (ignore.isNotEmpty()) arrayOf("extras", "--ignore", ignore) else arrayOf("extras")
            val extra = fossil(*extraArgs, cwd = workingFolder)
            changes.output.isEmpty() && extra.output.isEmpty()
        } catch (e: Exception) {
            logger.error("[fossil isRepoClean]: ${e.message}")
            false
        }
    }

    /**
     * 判断当前提交是否为初始化仓库时的提交
     */
    fun isRepoEmpty(latestCommit: Commit): Boolean {
        return latestCommit.branch == "trunk" &&
                latestCommit.comment == "initial empty check-in"
    }

    private fun parseMergeInfo(output: String = ""): List<MergeInfo> {
        val mergeInfoLinePattern = Regex("^(UPDATE|EDITED|ADDED|DELETED|CONFLICT|RENAMED|UNCHANGED)\\s+(.+)$")
        return output
            .split("\n")
            .map { it.trim() }
            .filter { it.isNotEmpty() }
            .mapNotNull { line ->
                val mergeInfoMatch = mergeInfoLinePattern.matchEntire(line)
                val status = mergeInfoMatch?.groups?.get(1)?.value
                val filePath = mergeInfoMatch?.groups?.get(2)?.value
                if (status != null && filePath != null) {
                    MergeInfo(status, filePath)
                } else {
                    null
                }
            }
    }

    fun cat(filePath: String, commitId: String?, workingFolder: String): String {
        var text = ""
        try {
            val args = if (commitId != null) {
                arrayOf("cat", filePath, "-r", commitId)
            } else {
                arrayOf("cat", filePath)
            }
            val res = fossil(*args, cwd = workingFolder)
            text = res.output
        } catch (_: Exception) {
            // Logger.warn(e.message) // Uncomment if you have a logger
        }
        return text
    }

    /**
     * 解析提交信息
     */
    private fun getCommitInfo(text: String): Commit {
        // 定义正则表达式
        val bracketPattern = Regex("\\[([a-f0-9]+),\\s*([a-zA-Z0-9_-]+)]") // 匹配 [hash, tag]
        val commentPattern = Regex("]\\s*([^\\n]+)") // 匹配 ] 后的文本直到 \n

        // 提取方括号中的数据
        val bracketMatch = bracketPattern.find(text)
        val commitId = bracketMatch?.groups?.get(1)?.value?.trim()
        val tag = bracketMatch?.groups?.get(2)?.value?.trim()

        // 提取方括号后的文本
        val commentMatch = commentPattern.find(text)
        val comment = commentMatch?.groups?.get(1)?.value?.trim()

        return Commit(commitId = commitId, branch = tag, comment = comment)
    }

    /**
     * 解析commitId
     */
    private fun getCommitId(text: String? = null): String? {
        val commitPattern = Regex("[0-9a-fA-F]{64}")
        val match = commitPattern.find(text ?: "")
        return match?.value?.trim()
    }

    fun diff(commitId: String, workingFolder: String, progressCallback: (progress: Double) -> Unit): List<MergeFile> {
        return try {
            val res = fossil("diff", "--brief", "-r", commitId, cwd = workingFolder)
            val infos = parseMergeInfo(res.output)
            val files = mutableListOf<MergeFile>()
            var count = 0
            for (info in infos) {
                if (info.status == "DELETED") {
                    val theirsContent = cat(info.filePath, commitId, workingFolder)
                    files.add(
                        MergeFile(
                            type = MergeStatus.IGNORED,
                            oursPath = "",
                            oursContent = "",
                            mergedPath = info.filePath,
                            mergedContent = theirsContent,
                            theirsContent = theirsContent
                        )
                    )
                }
                count++
                progressCallback(count * 1.0 / infos.size)
            }
            files
        } catch (e: Exception) {
            logger.error("[fossil diff]: ${e.message}")
            emptyList()
        }
    }

    /**
     * 获取当前目录的分支
     */
    fun getCurrentBranch(workingFolder: String): String? {
        return try {
            val res = fossil("branch", "current", cwd = workingFolder)
            res.output.split("\n").firstOrNull()
        } catch (e: Exception) {
            logger.error("[fossil getCurrentBranch]: ${e.message}")
            null
        }
    }

    /**
     * 获取最后一次提交的信息
     */
    fun getLatestCommit(workingFolder: String, branch: String? = null): Commit? {
        return try {
            val effectiveBranch = branch ?: getCurrentBranch(workingFolder)
            val args = arrayOf(
                "timeline",
                "-n", "1",
                "-F", "[%h, %b] %c",
                "-W", "0",
                "-t", "ci"
            ) + if (effectiveBranch?.isNotEmpty() == true) arrayOf("-b", effectiveBranch) else emptyArray()
            val res = fossil(*args, cwd = workingFolder)
            getCommitInfo(res.output)
        } catch (e: Exception) {
            logger.error("[fossil getLatestCommit]: ${e.message}")
            null
        }
    }

    /**
     * 根据message找commitid
     */
    fun getCommitIdWithComment(comment: String, workingFolder: String): String? {
        return try {
            val branchName = getCurrentBranch(workingFolder) ?: "trunk"
            val sqlQuery = "SELECT b.uuid FROM blob b JOIN event e ON b.rid = e.objid JOIN tagxref t ON b.rid = t.rid WHERE e.type = 'ci' AND e.comment = '$comment' AND t.tagid = 8 AND t.value = '$branchName'"
            val res = fossil("sql", sqlQuery, cwd = workingFolder)
            getCommitId(res.output)
        } catch (e: Exception) {
            logger.error("[fossil getCommitIdWithComment]: ${e.message}")
            null
        }
    }

    /**
     * 新建分支
     */
    fun newBranch(branchName: String, workingFolder: String, commitId: String? = null): Boolean {
        return try {
            val args = if (commitId != null) {
                arrayOf("branch", "new", branchName, "--user-override", BOT_NAME, commitId)
            } else {
                arrayOf("branch", "new", branchName, "--user-override", BOT_NAME)
            }
            val ret = fossil(*args, cwd = workingFolder)
            ret.error.isNullOrEmpty()
        } catch (e: Exception) {
            logger.error("[fossil newBranch]: ${e.message}")
            false
        }
    }

    /**
     * 检查分支是否存在
     */
    fun isBranchExist(branchName: String, workingFolder: String): Boolean {
        return try {
            val res = fossil("branch", "info", branchName, cwd = workingFolder)
            res.output.startsWith("$branchName: open as of")
        } catch (e: Exception) {
            logger.error("[fossil isBranchExist]: ${e.message}")
            false
        }
    }

    /**
     * 切换到分支
     */
    fun switchToBranch(branchName: String, workingFolder: String): Boolean {
        return try {
            fossil("update", branchName, cwd = workingFolder)
            true
        } catch (e: Exception) {
            logger.error("[fossil switchToBranch]: ${e.message}")
            false
        }
    }

    /**
     * 重命名分支
     */
    fun renameBranch(oldBranchName: String, newBranchName: String, branchFolder: String): Boolean {
        return try {
            val res = fossil("tag", "find", "sym-$oldBranchName", "--raw", cwd = branchFolder)
            val ids = res.output.split("\n").filter { it.isNotEmpty() }
            for (id in ids) {
                fossil("tag", "add", "sym-$newBranchName", id, "--raw", cwd = branchFolder)
                fossil("tag", "cancel", "sym-$oldBranchName", id, "--raw", cwd = branchFolder)
            }
            val ret = fossil(
                "sql",
                "UPDATE tagxref SET value = '$newBranchName' WHERE tagid = 8 AND value = '$oldBranchName'",
                cwd = branchFolder
            )
            ret.error.isNullOrEmpty()
        } catch (e: Exception) {
            logger.error("[fossil renameBranch]: ${e.message}")
            false
        }
    }

    /**
     * 删除分支
     */
    fun removeBranch(branchName: String, workingFolder: String): Boolean {
        return try {
            val res = fossil("tag", "find", "sym-$branchName", "--raw", cwd = workingFolder)
            val ids = res.output.split("\n").filter { it.isNotEmpty() }
            for (id in ids) {
                fossil("tag", "cancel", "sym-$branchName", id, "--raw", cwd = workingFolder)
            }
            val ret = fossil(
                "sql",
                "DELETE FROM tagxref WHERE tagid = 8 AND value = '$branchName'",
                cwd = workingFolder
            )
            ret.error.isNullOrEmpty()
        } catch (e: Exception) {
            logger.error("[fossil removeBranch]: ${e.message}")
            false
        }
    }

    /**
     * 提交
     */
    fun doCommit(workingFolder: String, comment: String, bot: Boolean): String? {
        return try {
            val args = arrayOf(
                "commit",
                "-m", comment,
                "--ignore-oversize",
                "--no-prompt",
                "--no-warnings",
                "--allow-empty"
            ) + if (bot) arrayOf("--user-override", BOT_NAME) else emptyArray()
            val res = fossil(*args, cwd = workingFolder)
            getCommitId(res.output)
        } catch (e: Exception) {
            logger.error("[fossil doCommit]: ${e.message}")
            null
        }
    }

    /**
     * 重命名文件夹
     */
    fun mvDirectory(oldName: String, newName: String, workingFolder: String, comment: String, bot: Boolean): String? {
        return try {
            fossil("rename", oldName, newName, cwd = workingFolder)
            doCommit(workingFolder, comment, bot)
        } catch (e: Exception) {
            logger.error("[fossil mvDirectory]: ${e.message}")
            null
        }
    }

    /**
     * 添加文件
     */
    fun addFile(file: Any, workingFolder: String, comment: String, bot: Boolean): String? {
        return try {
            val ignore = getIgnore(workingFolder)
            val fileArgs = when (file) {
                is String -> arrayOf(file)
                is List<*> -> file.filterIsInstance<String>().toTypedArray()
                else -> emptyArray()
            }
            val args = arrayOf("add", "--dotfiles", "--force") + fileArgs + if (ignore.isNotEmpty()) arrayOf("--ignore", ignore) else emptyArray()
            fossil(*args, cwd = workingFolder)
            doCommit(workingFolder, comment, bot)
        } catch (e: Exception) {
            logger.error("[fossil addFile]: ${e.message}")
            null
        }
    }

    private fun stageAllFiles(workingFolder: String, ignores: List<String>? = null): Boolean {
        return try {
            val ignore = getIgnore(workingFolder, ignores)
            val args = arrayOf("addremove", "--dotfiles") + if (ignore.isNotEmpty()) arrayOf("--ignore", ignore) else emptyArray()
            val ret = fossil(*args, cwd = workingFolder)
            ret.error.isNullOrEmpty()
        } catch (e: Exception) {
            logger.error("[fossil stageAllFiles]: ${e.message}")
            false
        }
    }

    /**
     * 提交所有本地修改
     */
    fun commitFiles(workingFolder: String, comment: String, bot: Boolean, ignores: List<String>? = null): String? {
        return try {
            if (stageAllFiles(workingFolder, ignores)) {
                doCommit(workingFolder, comment, bot)
            } else {
                null
            }
        } catch (e: Exception) {
            logger.error("[fossil commitFiles]: ${e.message}")
            null
        }
    }

    /**
     * 获取merge-info结果
     */
    private fun getMergeInfo(workingFolder: String): List<MergeInfo> {
        return try {
            val infoRes = fossil("merge-info", "-a", cwd = workingFolder)
            parseMergeInfo(infoRes.output)
        } catch (e: Exception) {
            logger.error("[fossil getMergeInfo]: ${e.message}")
            emptyList()
        }
    }

    /**
     * 获取changes结果
     */
    private fun getChangesInfo(workingFolder: String): List<ChangeInfo> {
        return try {
            val changesRes = fossil("changes", "--no-classify", cwd = workingFolder)
            val changesOutput = changesRes.output
            changesOutput
                .split("\n")
                .asSequence()
                .map { it.trim() }
                .filter { it.isNotEmpty() }
                .map { line ->
                    line
                        .split(" ")
                        .map { it.trim() }
                        .filter { it.isNotEmpty() }
                }
                .map { words ->
                    val arrowIndex = words.indexOf("->")
                    if (arrowIndex >= 0 && arrowIndex > 0 && arrowIndex + 1 < words.size) {
                        ChangeInfo(
                            fromPath = words[arrowIndex - 1],
                            toPath = words[arrowIndex + 1]
                        )
                    } else {
                        ChangeInfo(toPath = words.firstOrNull() ?: "")
                    }
                }
                .filter { it.toPath.isNotEmpty() }
                .toList()
        } catch (e: Exception) {
            logger.error("[fossil getChangesInfo]: ${e.message}")
            emptyList()
        }
    }

    private fun lsJavaFiles(commitId: String, moduleName: String, workingFolder: String): List<String>? {
        return try {
            val ret = fossil("ls", "-r", commitId, cwd = workingFolder)
            val paths = ret.output.split('\n')
            return paths.filter { isJava(it) && it.startsWith("$MODULE_FOLDER_NAME/$moduleName/") }
        } catch (e: Exception) {
            logger.error("[fossil ls]: ${e.message}")
            null
        }
    }

    private fun getMissingFiles(moduleName: String, mergeId: String, workingFolder: String): List<String> {
        val directoryPath = Paths.get(workingFolder, MODULE_FOLDER_NAME, moduleName).toString()
        val mergeFiles = lsJavaFiles(mergeId, moduleName, workingFolder) ?: emptyList()
        return mergeFiles.filter {
            !isFileInDirectory(Paths.get(workingFolder, it).toString(), directoryPath)
        }.toList()
    }

    /**
     * 获取指定文件的finfo
     */
    private fun getFinfoStatus(filePath: String, workingFolder: String): String? {
        return try {
            val finfoLinePattern = Regex("^(new|deleted|renamed|edited|unchanged)\\s+(.+)$")
            val finfoRes = fossil("finfo", "-s", filePath, cwd = workingFolder)
            val finfoMatch = finfoRes.output.trim().let { finfoLinePattern.matchEntire(it) }
            finfoMatch?.groups?.get(1)?.value
        } catch (e: Exception) {
            logger.error("[fossil getFinfoStatus]: ${e.message}")
            null
        }
    }

    /**
     * 获取所有merge的文件信息
     */
    private fun getMergeFiles(
        mergeId: String,
        workingFolder: String,
        moduleName: String,
        ignoreConflict: Boolean? = false,
        autoConflictResolve: Boolean? = false,
        codeMap: Map<String, String>? = null
    ): List<MergeFile> {
        // val latest = getLatestCommit(workingFolder)
        // val trunkId = latest?.commitId ?: ""
        val mergeInfos = getMergeInfo(workingFolder)
        val changesInfo = getChangesInfo(workingFolder)
        val missingFiles = getMissingFiles(moduleName, mergeId, workingFolder)

        val files = mutableListOf<MergeFile>()
        for (mergeInfo in mergeInfos) {
            try {
                val changeInfo = changesInfo.find { it.toPath == mergeInfo.filePath }
                val isBinary = BinaryRunner.isBinaryFile(Paths.get(workingFolder, mergeInfo.filePath).toString())
                if (isBinary !== false) {
                    continue
                }

                val status = getFinfoStatus(mergeInfo.filePath, workingFolder)
                var oursPath = changeInfo?.fromPath ?: mergeInfo.filePath
                var mergedPath = mergeInfo.filePath
                var type: MergeStatus = MergeStatus.UNCHANGED

                var conflictIgnored = false
                var oursContent = ""
                var theirsContent = ""
                var baseContent: String? = null
                var mergedContent = File(Paths.get(workingFolder, mergedPath).toString()).readText()

                when (mergeInfo.status) {
                    "CONFLICT" -> {
                        val baseFilePath = Paths.get(workingFolder, "$oursPath-baseline").toString()
                        val theirsFilePath = Paths.get(workingFolder, "$oursPath-merge").toString()
                        val oursFilePath = Paths.get(workingFolder, "$oursPath-original").toString()

                        val oursFile = File(oursFilePath)
                        val baseFile = File(baseFilePath)
                        val theirsFile = File(theirsFilePath)

                        baseContent = baseFile.readText()
                        theirsContent = theirsFile.readText()
                        oursContent = oursFile.readText()

                        if (ignoreConflict == true) {
                            // 在ignoreConflict为true时所有冲突以ours为准
                            conflictIgnored = true
                            mergedContent = oursContent
                            type = MergeStatus.UNCHANGED
                            val file = File(Paths.get(workingFolder, mergedPath).toString())
                            file.writeText(mergedContent)
                        } else {
                            type = MergeStatus.UNMERGED
                        }

                        baseFile.delete()
                        theirsFile.delete()
                        oursFile.delete()
                    }
                    else -> {
                        // var cachedCode: String? = null
                        // if (codeMap != null) {
                        //     cachedCode = codeMap[mergedPath] ?: ""
                        // }
                        // 暂时不cat文件，因为现在不会恢复文件或者查看无冲突的文件结果
                        // oursContent = cat(oursPath, trunkId, workingFolder)
                        // theirs就是生成的代码，利用传入的代码进行运行效率优化，减少一次cat操作
                        // theirsContent = cachedCode ?: cat(mergedPath, mergeId, workingFolder)
                        type = when {
                            mergeInfo.status == "ADDED" || status == "new" -> {
                                oursPath = ""
                                MergeStatus.ADDED
                            }
                            mergeInfo.status == "DELETED" || status == "deleted" -> {
                                mergedPath = ""
                                MergeStatus.DELETED
                            }
                            mergeInfo.status == "RENAMED" || status == "renamed" -> MergeStatus.RENAMED
                            mergeInfo.status == "UNCHANGED" || status == "unchanged" -> MergeStatus.UNCHANGED
                            else -> MergeStatus.MODIFIED
                        }
                    }
                }

                files.add(
                    MergeFile(
                        type = type,
                        oursPath = oursPath,
                        oursContent = oursContent,
                        mergedPath = mergedPath,
                        mergedContent = mergedContent,
                        theirsContent = theirsContent,
                        baseContent = baseContent,
                        conflictIgnored = conflictIgnored
                    )
                )
            } catch (e: Exception) {
                logger.error("[fossil getMergeFiles]: ${e.message}")
                continue
            }
        }

        if (missingFiles.isNotEmpty()) {
            val filePaths = files.map { it.mergedPath }
            missingFiles.forEach {
                if (!filePaths.contains(it)) {
                    var cachedCode: String? = null
                    if (codeMap != null) {
                        cachedCode = codeMap[it] ?: ""
                    }
                    val theirsContent = cachedCode ?: cat(it, mergeId, workingFolder)
                    files.add(
                        MergeFile(
                            type = MergeStatus.IGNORED,
                            oursPath = "",
                            oursContent = "",
                            mergedPath = it,
                            mergedContent = theirsContent,
                            theirsContent = theirsContent,
                            baseContent = theirsContent,
                        )
                    )
                }
            }
        }

        // 关闭，自动merge有bug，merge之后会出现两段重复的class定义
//        if (autoConflictResolve == true) {
//            val conflictJavaFiles = files.filter { it.type == MergeStatus.UNMERGED && isJava(it.mergedPath) }
//            if (conflictJavaFiles.isNotEmpty()) {
//                conflictJavaFiles.forEach { mergeFile ->
//                    val config = Config(
//                        mergeFile.oursContent,
//                        mergeFile.baseContent,
//                        mergeFile.theirsContent
//                    )
//                    config.parserConfig.keepComment = true
//                    val res = Driver.merge(config)
//                    if (res != null) {
//                        // 合并完没有冲突
//                        val textMergedContent = mergeFile.mergedContent
//                        mergeFile.mergedContent = res
//                        mergeFile.type = MergeStatus.MODIFIED
//                        mergeFile.conflictResolved = true
//                        logger.warn("[AutoConflictResolve] ================================")
//                        logger.warn("Fossil conflict:")
//                        logger.warn(textMergedContent)
//                        logger.warn("AutoConflictResolve:")
//                        logger.warn(mergeFile.mergedContent)
//                        logger.warn("[AutoConflictResolve] ================================")
//                        val file = File(Paths.get(workingFolder, mergeFile.mergedPath).toString())
//                        file.writeText(mergeFile.mergedContent)
//                    }
//                }
//            }
//        }

        return files
    }

    /**
     * merge指定commitId，结束后不提交
     */
    fun mergeCommit(
        workingFolder: String,
        commitId: String,
        moduleName: String,
        ignoreConflict: Boolean? = false,
        autoConflictResolve: Boolean? = false,
        codeMap: Map<String, String>? = null
    ): MergeResult {
        return try {
            fossil("merge", commitId, "-K", cwd = workingFolder)
            val files = getMergeFiles(commitId, workingFolder, moduleName, ignoreConflict, autoConflictResolve, codeMap)
            // merge出现冲突时会有额外的文件生成，在获取到三路的内容和这些文件被删掉
            // 有时候这些文件会被add到fossil的管理中导致删除后出现missing
            // 所以在处理完merge信息删掉多余的文件后需要stage一次
            if (stageAllFiles(workingFolder)) {
                MergeResult(
                    status = if (files.any { it.type == MergeStatus.UNMERGED }) MergeResultStatus.Conflict else MergeResultStatus.Success,
                    files = files
                )
            } else {
                MergeResult(status = MergeResultStatus.Failed)
            }
        } catch (e: Exception) {
            logger.error("[fossil mergeCommit]: ${e.message}")
            MergeResult(status = MergeResultStatus.Failed)
        }
    }

    /**
     * 清理工作目录
     */
    fun clean(workingFolder: String): Boolean {
        return try {
            val ignore = getIgnore(workingFolder)
            val args = arrayOf("clean", "-x") + if (ignore.isNotEmpty()) arrayOf("--ignore", ignore) else emptyArray()
            val ret = fossil(*args, cwd = workingFolder)
            ret.error.isNullOrEmpty()
        } catch (e: Exception) {
            logger.error("[fossil clean]: ${e.message}")
            false
        }
    }

    fun revert(workingFolder: String): Boolean {
        return try {
            val ret = fossil("revert", "--noundo", cwd = workingFolder)
            ret.error.isNullOrEmpty()
        } catch (e: Exception) {
            logger.error("[fossil revert]: ${e.message}")
            false
        }
    }

    private fun extractChildCommitId(fossilInfoOutput: String): String? {
        val lines = fossilInfoOutput.split("\n")
        for (line in lines) {
            if (line.trim().startsWith("child:")) {
                return line.substringAfter("child:").trim().split(" ")[0]
            }
        }
        return null
    }

    private fun extractPurgeId(fossilPurgeOutput: String): String? {
        val lines = fossilPurgeOutput.split("\n")
        for (line in lines) {
            if (line.contains("purge undo")) {
                // 提取 purge undo 后面的数字
                val regex = Regex("""purge undo (\d+)""")
                val match = regex.find(line)
                return match?.groups?.get(1)?.value
            }
        }
        return null
    }

    fun purge(workingFolder: String): Boolean {
        return try {
            val info = fossil("info", cwd = workingFolder)
            if (info.error.isNullOrEmpty()) {
                val childCommitId = extractChildCommitId(info.output)
                if (childCommitId.isNullOrEmpty()) {
                    // 没有child commit
                    true
                } else {
                    val ret = fossil("purge", "checkins", childCommitId, cwd = workingFolder)
                    val success = ret.error.isNullOrEmpty()
                    if (success) {
                        // 清理purge历史
                        val purgeId = extractPurgeId(ret.output)
                        if (!purgeId.isNullOrEmpty()) {
                            fossil("purge", "obliterate", "--force", cwd = workingFolder)
                        }
                    }
                    success
                }
            } else {
                logger.error("[fossil purge]: info error - ${info.error}")
                false
            }
        } catch (e: Exception) {
            logger.error("[fossil purge]: ${e.message}")
            false
        }
    }

    fun rollback(workingFolder: String, commitId: String, soft: Boolean = true): Boolean {
        // 回退到指定版本
        val type = if(soft) "soft" else "hard"
        try {
            var result = true
            if (!soft) {
                // hard需要清理掉当前的所有修改状态
                if (!revert(workingFolder)) {
                    logger.warn("[fossil rollback $type]: hard revert failed")
                    result = false
                }
                if (!clean(workingFolder)) {
                    logger.warn("[fossil rollback $type]: clean failed")
                    result = false
                }
            }
            if (!updateRepo(workingFolder, commitId)) {
                logger.error("[fossil rollback $type]: update failed")
                result = false
            }
            if (soft) {
                if (!revert(workingFolder)) {
                    logger.warn("[fossil rollback $type]: soft revert failed")
                    result = false
                }
            } else {
                if (!purge(workingFolder)) {
                    logger.warn("[fossil rollback $type]: purge failed")
                    result = false
                }
            }
            if (!clean(workingFolder)) {
                logger.warn("[fossil rollback $type]: clean failed")
                result = false
            }
            return result
        } catch (e: Exception) {
            logger.error("[fossil rollback $type]: ${e.message}")
            return false
        }
    }

    private fun parseFossilBlame(blameOutput: String): List<BlameEntry> {
        // 正则表达式：匹配 fossil blame 的每行
        val regex = Regex("^([0-9a-f]+)\\s+(\\d{4}-\\d{2}-\\d{2})\\s+([^:]+):\\s*(.*)$")

        // 按行分割并解析
        return blameOutput.trim().lines()
            .mapNotNull { line ->
                val match = regex.matchEntire(line)
                match?.let {
                    BlameEntry(
                        commitId = it.groups[1]!!.value,
                        time = it.groups[2]!!.value,
                        user = it.groups[3]!!.value,
                        code = it.groups[4]!!.value
                    )
                }
            }
    }

    fun blame(file: String, workingFolder: String): List<BlameEntry>? {
        return try {
            val ret = fossil("blame", "-n", "none", "-w", "-Z", file, cwd = workingFolder)
            parseFossilBlame(ret.output)
        } catch (e: Exception) {
            logger.error("[fossil blame]: ${e.message}")
            null
        }
    }

    /**
     * 创建存档点，返回revert到存档点的方法
     */
    fun archive(
        workingFolder: String,
        preCommitMessage: String? = null
    ): ((folder: String?) -> Unit)? {
        val isClean = isRepoClean(workingFolder)
        if (!isClean) {
            val preCommitId = commitFiles(
                workingFolder,
                preCommitMessage ?: "pre commit",
                bot = false
            )
            if (preCommitId == null) {
                return null
            }
        }
        val latest = getLatestCommit(workingFolder)
        val commitId = latest?.commitId ?: return null

        return { folder: String? ->
            val path = folder ?: workingFolder
            rollback(path, commitId, true)
        }
    }
}