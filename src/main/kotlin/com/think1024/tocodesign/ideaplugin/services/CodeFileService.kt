package com.think1024.tocodesign.ideaplugin.services

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.JarFileSystem
import com.intellij.psi.PsiManager
import com.think1024.tocodesign.ideaplugin.utils.FileUtil
import com.think1024.tocodesign.ideaplugin.utils.BackgroundTaskUtil
import java.io.IOException
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.compiler.CompilerManager
import com.intellij.openapi.compiler.CompilerMessageCategory
import com.intellij.openapi.fileEditor.FileEditorManager
import com.think1024.tocodesign.ideaplugin.services.codebase.CodeBaseUtils
import com.intellij.psi.impl.compiled.ClsFileImpl
import com.intellij.openapi.progress.ProcessCanceledException

@Service(Service.Level.PROJECT)
class CodeFileService(private val project: Project) {
    private val logger = Logger.getInstance(CodeFileService::class.java)

    data class MethodInfo(
        val content: String,         // 方法内容（包含方法声明和方法体）
        val startLine: Int,          // 方法起始行号（1基）
        val endLine: Int,            // 方法结束行号（1基）
        val signature: String = ""   // 方法签名，用于区分重载方法
    )

    data class ClassMethodResult(
        val content: String,             // 完整提取内容（包括上下文）
        val methodInfos: List<MethodInfo> // 所有同名方法的信息
    )

    data class BuildResult(
        val success: Boolean,           // 构建是否成功
        val aborted: Boolean,          // 构建是否被中止
        val errorCount: Int,           // 错误数量
//        val warningCount: Int,         // 警告数量
        val errors: List<String>,      // 错误消息列表
//        val warnings: List<String>     // 警告消息列表
    )

    /**
     * 获取多个文件的内容 (同步版本)
     * 
     * @param filePaths 文件路径数组
     * @return 文件路径到内容的映射，如果文件不存在或读取失败，对应的值为null
     */
    fun getFilesContent(filePaths: Array<String>): Map<String, String?> {
        val result = mutableMapOf<String, String?>()
        
        for (path in filePaths) {
            try {
                val content = getFileContent(path)
                result[path] = content
            } catch (e: Exception) {
                logger.warn("Failed to read file: $path", e)
                result[path] = null
            }
        }
        
        return result
    }
    
    /**
     * 获取多个文件的内容 (异步版本)
     * 
     * @param filePaths 文件路径数组
     * @param callback 回调函数，接收文件内容映射作为参数
     */
    fun getFilesContentAsync(filePaths: Array<String>, callback: (Map<String, String?>) -> Unit) {
        BackgroundTaskUtil.executeInBackground(
            project = project,
            taskName = "Reading Files",
            action = {
                getFilesContent(filePaths)
            },
            onSuccess = { result ->
                callback(result)
            },
            onFailure = { e ->
                logger.warn("Failed to read files", e)
                callback(emptyMap())
            }
        )
    }

    /**
     * 获取单个文件的内容
     * 
     * @param filePath 文件路径
     * @param startLine 可选，起始行号（1基），如果为null则从文件开头开始
     * @param endLine 可选，结束行号（1基），如果为null则到文件结尾结束
     * @return 文件内容
     * @throws IOException 如果文件不存在或读取失败
     */
    fun getFileContent(filePath: String, startLine: Int? = null, endLine: Int? = null): String? {
        // 获取当前project
        val virtualFile = FileUtil.getVirtualFile(filePath, project)

        // 增加兜底逻辑，如果vfs没找到，再尝试用FileIO去读取文件内容
        if (virtualFile == null) {
            return readFileContentWithFileIO(filePath, startLine, endLine)
        }

        return getFileContent(virtualFile, startLine, endLine)
    }

    /**
     * 从内容中提取指定行范围的内容
     *
     * @param content 原始内容
     * @param startLine 可选，起始行号（1基），如果为null则从文件开头开始
     * @param endLine 可选，结束行号（1基），如果为null则到文件结尾结束
     * @return 截取后的内容，如果没有指定行范围则返回原始内容
     */
    private fun extractLinesFromContent(content: String, startLine: Int? = null, endLine: Int? = null): String {
        if (startLine != null || endLine != null) {
            val lines = content.lines()
            val start = (startLine?.coerceAtLeast(1) ?: 1) - 1 // 转为0基索引
            val end = (endLine?.coerceAtMost(lines.size) ?: lines.size) - 1 // 转为0基索引

            if (start <= end && start < lines.size) {
                return lines.subList(start, end + 1).joinToString("\n")
            }
        }
        return content
    }

    /**
     * 使用FileIO读取指定文件的内容，如果传入可选的参数startLine和endLine，则只读取指定行范围的内容
     */
    fun readFileContentWithFileIO(filePath: String, startLine: Int? = null, endLine: Int? = null): String? {
        return try {
            val absolutePath = resolveToAbsolutePath(filePath)

            val file = java.io.File(absolutePath)
            if (!file.exists() || !file.isFile) {
                return null
            }

            val content = file.readText()
            extractLinesFromContent(content, startLine, endLine)
        } catch (e: Exception) {
            logger.warn("Failed to read file with FileIO: $filePath", e)
            null
        }
    }

    /**
     * 将相对路径转换为绝对路径，如果已经是绝对路径则直接返回
     *
     * @param filePath 文件路径（相对或绝对）
     * @return 绝对路径
     */
    private fun resolveToAbsolutePath(filePath: String): String {
        return if (java.io.File(filePath).isAbsolute) {
            filePath
        } else {
            // 如果是相对路径，基于项目路径解析
            val basePath = project.basePath
            if (basePath != null) {
                java.nio.file.Paths.get(basePath, filePath).toString()
            } else {
                filePath
            }
        }
    }

    /**
     * 获取单个文件的内容
     * 
     * @param file 虚拟文件对象
     * @param startLine 可选的起始行号（1基），如果为null则从文件开头开始
     * @param endLine 可选的结束行号（1基），如果为null则读到文件结尾
     * @return 文件内容
     * @throws IOException 如果文件不存在或读取失败
     */
    fun getFileContent(file: VirtualFile, startLine: Int? = null, endLine: Int? = null): String? {
        return try {
            if (!file.isValid || file.isDirectory) {
                return null
            }
            
            // 检查是否是二进制文件
            if (file.fileType.isBinary) {
                return null
            }

            val content = String(file.contentsToByteArray())
            extractLinesFromContent(content, startLine, endLine)
        } catch (e: Exception) {
            logger.warn("Failed to read file: ${file.path}", e)
            null
        }
    }

    /**
     * 获取指定文件夹中的所有Java文件名称列表（以相对路径形式）
     *
     * @param folderPath 文件夹路径
     * @param recursive 是否递归查找子文件夹中的文件
     * @return Java文件名称列表（相对路径），如果文件夹不存在则返回空列表
     */
    fun getFilesInFolder(
        folderPath: String,
        recursive: Boolean,
        excludePatterns: List<String> = listOf(".gitkeep")
    ): List<String> {
        val result = mutableListOf<String>()

        // 获取文件夹对应的虚拟文件
        val folderFile = FileUtil.getVirtualFile(folderPath, project) ?: return result

        if (!folderFile.isDirectory) {
            logger.warn("指定路径不是文件夹: $folderPath")
            return result
        }

        // 基准路径，用于计算相对路径
        val basePath = folderFile.path

        // 收集文件
        collectFiles(folderFile, basePath, recursive, excludePatterns, result)

        return result
    }

    /**
     * 从文件中提取指定方法的内容
     * @param filePath 文件路径
     * @param methodName 方法全名，格式为 package.class.MethodName
     * @return 方法内容和行号信息，未找到时返回null
     */
    fun readClassMethod(filePath: String, methodName: String): ClassMethodResult? {
        // 尝试获取virtual file，里面会触发一次索引刷新
        FileUtil.getVirtualFile(filePath, project)

        // 使用 DumbService 提供的正确方法
        val dumbService = DumbService.getInstance(project)

        // 如果当前处于 dumb 模式，等待索引完成
        if (dumbService.isDumb) {
            // 在UI线程中使用 waitForSmartMode 会阻塞UI，但在后台线程中使用是安全的
            dumbService.waitForSmartMode()
        }

        // 在索引完成后（smart模式下）执行读操作
        return ApplicationManager.getApplication().runReadAction<ClassMethodResult?> {
            val virtualFile = FileUtil.getVirtualFile(filePath, project) ?: return@runReadAction null
            readClassMethod(virtualFile, methodName)
        }
    }

    /**
     * 从文件中提取指定方法的内容及其行号信息
     * @param file 虚拟文件
     * @param methodName 方法名，格式为 package.class.MethodName
     * @return 方法内容和行号信息，未找到时返回null
     */
    fun readClassMethod(file: VirtualFile, methodName: String): ClassMethodResult? {
        if (!file.isValid || file.isDirectory) {
            logger.warn("无效的文件或目录: ${file.path}")
            return null
        }

        val parts = methodName.split(".")
        if (parts.size < 2) {
            logger.warn("无效的方法名格式，应为 package.class.MethodName: $methodName")
            return null
        }

        val simpleMethodName = parts.last()
        val className = parts[parts.size - 2]

        // 确保在读操作内执行PSI相关操作
        return ApplicationManager.getApplication().runReadAction<ClassMethodResult?> {
            when (file.extension?.lowercase()) {
                "java" -> extractJavaMethodContent(file, className, simpleMethodName)
                else -> null
            }
        }
    }

    /**
     * 创建指定路径的文件，对于Java文件自动添加包声明和类声明
     *
     * @param filePath 要创建的文件路径
     * @param content 文件内容，默认为null（创建空文件或根据文件类型生成默认内容）
     * @return 结果对象，包含是否成功和错误消息
     */
    fun createFile(filePath: String, content: String? = null): FileOperationResult {
        try {
            // 检查文件是否已存在
            val existingFile = FileUtil.getVirtualFile(filePath, project)
            if (existingFile != null && existingFile.exists()) {
                return FileOperationResult(false, "文件已存在: $filePath")
            }

            // 处理路径，转换相对路径为绝对路径
            val absolutePath = resolveToAbsolutePath(filePath)

            // 创建父目录
            val ioFile = java.io.File(absolutePath)
            val parentDir = ioFile.parentFile
            if (parentDir != null && !parentDir.exists()) {
                if (!parentDir.mkdirs()) {
                    return FileOperationResult(false, "无法创建父目录: ${parentDir.path}")
                }
            }

            // 确定文件内容
            val fileContent = when {
                content != null -> content // 如果提供了内容，直接使用
                filePath.endsWith(".java") -> generateJavaFileContent(filePath) // Java文件默认内容
                else -> "" // 其他类型文件默认为空
            }

            // 创建结果变量
            var result: FileOperationResult? = null

            // 确保在EDT线程上执行UI操作
            if (ApplicationManager.getApplication().isDispatchThread) {
                // 如果已经在EDT线程，直接执行写操作
                result = executeFileCreation(parentDir, ioFile, fileContent)
            } else {
                // 否则，在EDT线程上同步执行
                ApplicationManager.getApplication().invokeAndWait {
                    result = executeFileCreation(parentDir, ioFile, fileContent)
                }
            }

            // 当result是success时，记录文件初始版本
//            if (result?.success == true) {
//                FileOriginalVersionService.getInstance(project).markAsNewFile(filePath)
//            }
            return result ?: FileOperationResult(false, "操作结果未知")
        } catch (e: ProcessCanceledException) {
            // 必须重新抛出 ProcessCanceledException
            throw e
        } catch (e: Exception) {
            return FileOperationResult(false, "创建文件异常: ${e.message}")
        }
    }

    /**
     * 执行文件创建的实际操作（在EDT线程中调用）
     */
    private fun executeFileCreation(parentDir: java.io.File, ioFile: java.io.File, fileContent: String): FileOperationResult {
        var result: FileOperationResult? = null

        try {
            ApplicationManager.getApplication().runWriteAction {
                try {
                    // 刷新父目录以确保VFS能看到它
                    val parentVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByPath(parentDir.absolutePath)
                        ?: return@runWriteAction

                    val newFile = parentVirtualFile.createChildData(this, ioFile.name)
                    newFile.setBinaryContent(fileContent.toByteArray())
                    result = FileOperationResult(true, "文件创建成功", content = fileContent)
                } catch (e: Exception) {
                    result = FileOperationResult(false, "创建文件失败: ${e.message}")
                }
            }
        } catch (e: com.intellij.openapi.progress.ProcessCanceledException) {
            // 必须重新抛出 ProcessCanceledException
            throw e
        } catch (e: Exception) {
            result = FileOperationResult(false, "文件创建操作异常: ${e.message}")
        }

        return result ?: FileOperationResult(false, "文件创建操作未完成")
    }

    /**
     * 为Java文件生成默认内容（包声明和类声明）
     *
     * @param filePath Java文件路径
     * @return 生成的Java文件内容
     */
    private fun generateJavaFileContent(filePath: String): String {
        // 提取文件名（不带扩展名）作为类名
        val fileName = java.io.File(filePath).name
        val className = fileName.substringBeforeLast(".java")

        // 根据文件路径确定包名
        val packageName = determinePackageName(filePath)

        // 构建Java文件内容
        val sb = StringBuilder()

        // 添加包声明
        if (packageName.isNotEmpty()) {
            sb.append("package $packageName;\n\n")
        }

        // 添加类声明
        sb.append("public class $className {\n")
        sb.append("    // TODO: 实现类内容\n")
        sb.append("}\n")

        return sb.toString()
    }

    /**
     * 根据文件路径确定Java包名
     *
     * @param filePath Java文件路径
     * @return 包名
     */
    private fun determinePackageName(filePath: String): String {
        // 如果是相对路径，基于项目路径解析
        val absolutePath = resolveToAbsolutePath((filePath))

        // 查找src目录位置
        val srcIndex = absolutePath.indexOf("/src/")
        if (srcIndex == -1) {
            return "" // 如果没有找到src目录，返回空包名
        }

        // 截取src/main/java或src之后的路径部分
        val packagePath = if (absolutePath.contains("/src/main/java/")) {
            absolutePath.substringAfter("/src/main/java/").substringBeforeLast("/")
        } else {
            val afterSrc = absolutePath.substringAfter("/src/")
            if (afterSrc.contains('/')) {
                // 跳过第一个目录（如test）和文件名
                afterSrc.substringAfter("/").substringBeforeLast("/")
            } else {
                ""
            }
        }

        // 将路径分隔符替换为包分隔符
        return packagePath.replace('/', '.')
    }

    /**
     * 文件操作结果类
     */
    data class FileOperationResult(
        val success: Boolean,
        val message: String,
        val content: String? = null
    )

    /**
     * 文件删除操作结果类
     */
    data class RemoveFilesResult(
        val success: Boolean,
        val message: String,
        val successFiles: List<String> = emptyList(),
        val failedFiles: List<String> = emptyList(),
        val totalCount: Int = 0,
        val successCount: Int = 0
    )

    /**
     * 接受变更操作结果类
     */
    data class AcceptChangesResult(
        val success: Boolean,
        val successCount: Int = 0,
        val failedFiles: List<String> = emptyList(),
        val successFiles: List<String> = emptyList()
    )

    /**
     * 类文件内容结果
     * 包含类文件本身的内容以及所有父类内容
     */
    data class ClassFileContent(
        val classContent: String,            // 类文件本身的内容
        val parentClassName: String? = null,  // 父类名称
        val parentClassContent: String? = null, // 父类内容
        val fileType: String? = null        // 文件类型（扩展名）
    )

    /**
     * 读取指定类文件的内容，并返回其基类内容
     *
     * @param classFilePath 类文件路径
     * @return 类文件内容结果对象，包含类本身内容和父类内容
     */
    fun readFile(classFilePath: String, startLine: Int? = null, endLine: Int? = null, virtualFile: VirtualFile? = null): ClassFileContent? {
        // 检查是否是 jar 包中的文件
        val absoluteFilePath = virtualFile?.path
        if (absoluteFilePath != null) {
            val isInJar = absoluteFilePath.startsWith("jar://") || absoluteFilePath.contains(".jar!")
            if (isInJar) {
                val classContent = readJarFileContent(virtualFile)
                return classContent
            }
        }

        // 如果 virtualFile 为 null，但 classFilePath 是 jar 包路径，尝试处理 jar 包文件
        if (virtualFile == null && classFilePath.contains(".jar!")) {
            val jarVirtualFile = getJarVirtualFile(classFilePath)
            if (jarVirtualFile != null) {
                val classContent = readJarFileContent(jarVirtualFile)
                return classContent
            }
        }

        // 读取主类文件内容
        val classContent = getFileContent(classFilePath, startLine, endLine) ?: return null

        // 确保在ReadAction内执行PSI相关操作
        return ApplicationManager.getApplication().runReadAction<ClassFileContent?> {
            val virtualFile = FileUtil.getVirtualFile(classFilePath, project) ?: return@runReadAction null

            if (!virtualFile.isValid) {
                return@runReadAction ClassFileContent(classContent, classFilePath)
            }

            val psiManager = PsiManager.getInstance(project)
            val psiFile = psiManager.findFile(virtualFile) ?: return@runReadAction ClassFileContent(classContent, classFilePath)

            // 获取父类信息
            val parentClass = when (virtualFile.extension?.lowercase()) {
                "java" -> findJavaParentClasses(psiFile)
                else -> null
            }

            ClassFileContent(
                classContent = classContent,
                parentClassName = parentClass?.first,
                parentClassContent = parentClass?.second,
                fileType = virtualFile.extension?.lowercase()
            )
        }
    }

    /**
     * 查找Java类的父类信息
     * @param psiFile PSI文件
     * @return 父类名称和内容的对
     */
    private fun findJavaParentClasses(psiFile: com.intellij.psi.PsiFile): Pair<String, String?>? {
        val javaFile = psiFile as? com.intellij.psi.PsiJavaFile ?: return null

        for (psiClass in javaFile.classes) {
            // 获取父类
            val superClass = psiClass.superClass ?: continue
            val superClassName = superClass.qualifiedName ?: continue

            // 跳过java.lang.Object
            if (superClassName == "java.lang.Object") continue

            // 跳过BaseXXXBOService, 判断时去掉包名
            val simpleName = superClassName.substringAfterLast('.')
            if (simpleName.startsWith("Base") && simpleName.endsWith("BOService")) continue

            // 获取父类的文件
            val superClassFile = superClass.containingFile?.virtualFile
            if (superClassFile != null) {
                // 读取父类内容
                val superClassContent = getFileContent(superClassFile)
                return Pair(superClassName, superClassContent)
            } else {
                // 可能是外部库类，标记为不可用
                return Pair(superClassName, null)
            }
        }

        return null
    }

    /**
     * 从Java文件中提取方法内容，包含上下文信息和行号
     *
     * @param file 虚拟文件
     * @param className 类名
     * @param methodName 方法名
     * @return 包含上下文信息的方法内容和行号信息，未找到时返回null
     * @note 该方法必须在ReadAction中调用
     */
    private fun extractJavaMethodContent(file: VirtualFile, className: String, methodName: String): ClassMethodResult? {
        // 确保在ReadAction内执行
        if (!ApplicationManager.getApplication().isReadAccessAllowed) {
            logger.warn("extractJavaMethodContent必须在ReadAction内调用")
            return null
        }

        val psiManager = PsiManager.getInstance(project)
        val psiFile = psiManager.findFile(file) ?: return null
        val javaFile = psiFile as? com.intellij.psi.PsiJavaFile ?: return null
        val packageName = javaFile.packageName
        val imports = javaFile.importList?.importStatements?.joinToString("\n") { it.text } ?: ""
        val psiClass = javaFile.classes.find { it.name == className } ?: return null
        val fileText = javaFile.text
        val fileLines = fileText.lines()

        // 查找所有同名方法
        val methods = psiClass.methods.filter { it.name == methodName }
        if (methods.isEmpty()) return null

        // 获取类声明（包含注释和注解）
        val classDeclStartOffset = psiClass.textRange.startOffset
        val classDeclStartLine = fileText.substring(0, classDeclStartOffset).count { it == '\n' }
        // 类定义行（含注释/注解）
        val classDeclEndOffset = psiClass.lBrace?.textOffset ?: psiClass.textRange.startOffset
        val classDeclEndLine = fileText.substring(0, classDeclEndOffset).count { it == '\n' }

        // 获取所有字段（包括它们之间的空行和注释）
        val fields = psiClass.fields
        val fieldsRange = if (fields.isNotEmpty()) {
            val firstField = fields.first()
            val lastField = fields.last()
            val fieldsStartLine = fileText.substring(0, firstField.textRange.startOffset).count { it == '\n' }
            val fieldsEndLine = fileText.substring(0, lastField.textRange.endOffset).count { it == '\n' }
            fieldsStartLine to fieldsEndLine
        } else {
            null
        }

        // 构建结果
        val result = StringBuilder()
        // 1. 包名
        result.append("package $packageName;\n\n")
        // 2. import
        if (imports.isNotEmpty()) {
            result.append("$imports\n\n")
        }
        // 3. 类声明（包含注释和注解）
        for (i in classDeclStartLine..classDeclEndLine) {
            result.append(fileLines[i]).append("\n")
        }

        // 4. 类的成员变量（如果存在）
        fieldsRange?.let { (start, end) ->
            // 添加一个空行
            result.append("\n")
            // 添加所有字段（包括它们之间的空行和注释）
            for (i in start..end) {
                result.append(fileLines[i]).append("\n")
            }
            // 添加一个空行
            result.append("\n")
        }

        val existingCodeStr = "  // ...existing code ...\n\n"
        // 5. 省略其余部分
        result.append(existingCodeStr)

        // 6. 收集所有同名方法信息
        val methodInfos = mutableListOf<MethodInfo>()
        methods.forEach { method ->
            val methodRange = method.textRange
            val methodStartLine = fileText.substring(0, methodRange.startOffset).count { it == '\n' } + 1 // 转换为1基
            val methodEndLine = fileText.substring(0, methodRange.endOffset).count { it == '\n' } + 1 // 转换为1基

            // 获取方法签名（区分重载方法）
            val paramTypes = method.parameterList.parameters.joinToString(", ") { it.type.presentableText }
            val signature = "$methodName($paramTypes)"

            // 提取方法内容
            val methodContent = StringBuilder()
            for (i in methodStartLine - 1 until methodEndLine) { // 转回0基索引
                methodContent.append(fileLines[i]).append("\n")
            }

            // 添加到结果列表
            methodInfos.add(MethodInfo(
                content = methodContent.toString(),
                startLine = methodStartLine,
                endLine = methodEndLine,
                signature = signature
            ))

            // 同时添加到完整结果
            result.append(methodContent).append("\n")
        }

        // 7. 省略其余部分
        result.append(existingCodeStr)
        // 8. 类结束括号
        if (!result.toString().trim().endsWith("}")) {
            result.append("}\n")
        }

        return ClassMethodResult(
            content = result.toString(),
            methodInfos = methodInfos
        )
    }


    /**
     * 递归收集文件
     *
     * @param currentFolder 当前文件夹
     * @param basePath 基准路径
     * @param recursive 是否递归查找
     * @param result 结果列表
     */
    private fun collectFiles(
        currentFolder: VirtualFile,
        basePath: String,
        recursive: Boolean,
        excludePatterns: List<String>,
        result: MutableList<String>,
    ) {
        try {
            for (child in currentFolder.children) {
                if (child.isDirectory) {
                    if (recursive) {
                        collectFiles(child, basePath, true, excludePatterns, result)
                    }
                } else {
                    // 计算相对路径
                    val relativePath = child.path.removePrefix(basePath).removePrefix("/")

                    // 检查文件是否应该被排除
                    val fileName = child.name
                    val shouldExclude = excludePatterns.any { pattern ->
                        // 简单的精确匹配
                        if (!pattern.contains("*")) {
                            fileName == pattern
                        } else {
                            // 简单的通配符匹配，将 * 转换为正则表达式
                            val regex = pattern.replace(".", "\\.").replace("*", ".*")
                            fileName.matches(regex.toRegex())
                        }
                    }

                    // 只添加Java文件且不被排除的文件
                    if (!shouldExclude && child.extension?.lowercase() == "java") {
                        result.add(relativePath)
                    }
                }
            }
        } catch (e: Exception) {
            logger.warn("读取文件夹内容时出错: ${currentFolder.path}", e)
        }
    }

    fun editFile(filePath: String, newCode: String): CodeMergeService.ApplyChangesResult {
        // 将mergedCode写回文件
        try {
            // FIXME: 测试日志
            logger.info("editFile: $filePath, newCode: ${newCode}")

            System.gc()
//            // 在首次调用时记录原始版本
//            val versionService = FileOriginalVersionService.getInstance(project)
//            versionService.recordOriginalVersion(filePath)

            val codeMergeService = CodeMergeService.getInstance(project)
            val result = codeMergeService.applyChangesToFile(filePath, newCode)
            System.gc()

            // 写入成功后，关闭inline diff的显示，改为用idea自带的diff界面
//            if (result.success) {
//                // 写入成功，则唤起inlineDiff
//                showInlineDiff(filePath)
//            }
            return result
        } catch (e: Exception) {
            logger.warn("Failed to write file: ${filePath}", e)
            return CodeMergeService.ApplyChangesResult(
                isSuccess = false,
                message = "写入文件失败: ${e.message}",
            )
        }
    }

    /**
     * 显示文件的原始版本（首次编辑前）与当前版本的差异
     *
     * @param filePath 文件路径
     * @return 是否成功显示差异（如果没有原始版本则返回false）
     */
    fun showOriginalDiff(filePath: String, originContent: String? = null, commitId: String?): Boolean {
        val versionService = FileOriginalVersionService.getInstance(project)
        return versionService.showOriginalDiff(filePath, originContent, commitId)
    }

    /**
     * 在源文件窗口中显示内联diff
     *
     * @param filePath 文件路径
     * @return 是否成功显示内联diff
     */
    fun showInlineDiff(filePath: String, originContent: String? = null): Boolean {
        val versionService = FileOriginalVersionService.getInstance(project)
        return versionService.showInlineDiff(filePath, originContent)
    }

    /**
     * 检查指定文件的编译错误（异步版本）
     * 修改为单独编译每个文件，避免在遇到编译错误时中止后续文件的编译
     *
     * @param filePaths 要检查的文件路径列表（相对于项目根目录）
     * @param callback 检查完成后的回调函数
     */
    @Deprecated("当前方法是对每个文件各自单独做compile，使用 checkFilesCompileErrors 方法替代", ReplaceWith("checkFilesCompileErrors(filePaths, callback)"))
    fun checkFilesCompileErrorsSeparately(filePaths: List<String>, callback: (BuildResult) -> Unit) {
        try {
            val compilerManager = CompilerManager.getInstance(project)
            val allErrors = mutableListOf<String>()
            var totalErrorCount = 0
            var hasAborted = false

            ApplicationManager.getApplication().invokeLater {
                // 将相对路径转换为 VirtualFile 对象
                val virtualFiles = filePaths.mapNotNull { relativePath ->
                    val absolutePath = if (project.basePath != null) {
                        "${project.basePath}/$relativePath"
                    } else {
                        relativePath
                    }
                    val virtualFile = LocalFileSystem.getInstance().findFileByPath(absolutePath)
                    if (virtualFile != null) {
                        relativePath to virtualFile
                    } else {
                        null
                    }
                }

                if (virtualFiles.isEmpty()) {
                    val result = BuildResult(
                        success = false,
                        aborted = false,
                        errorCount = 1,
                        errors = listOf("未找到指定的文件: ${filePaths.joinToString(", ")}")
                    )
                    callback(result)
                    return@invokeLater
                }

                // 用于跟踪已完成的编译任务数量
                var completedCount = 0
                val totalCount = virtualFiles.size

                // 单独编译每个文件
                virtualFiles.forEach { (relativePath, virtualFile) ->
                    compilerManager.compile(arrayOf(virtualFile)) { aborted, errorCount, warningCount, context ->
                        // 收集当前文件的编译错误
                        val errorMessages = context.getMessages(CompilerMessageCategory.ERROR)

                        errorMessages.forEach { message ->
                            val navigatable = message.navigatable
                            // 获取文件路径
                            val absolutePath = message.virtualFile?.path ?: "未知文件"

                            // 将绝对路径转换为相对于项目根目录的相对路径
                            val filePath = if (absolutePath != "未知文件" && project.basePath != null) {
                                val basePath = project.basePath!!
                                if (absolutePath.startsWith(basePath)) {
                                    absolutePath.substring(basePath.length).removePrefix("/")
                                } else {
                                    absolutePath
                                }
                            } else {
                                absolutePath
                            }

                            if (navigatable != null && navigatable is com.intellij.openapi.fileEditor.OpenFileDescriptor) {
                                // 获取行号和列号 (IntelliJ的行号是0基，需要+1转成1基)
                                val lineNumber = navigatable.line + 1
                                val column = navigatable.column + 1
                                // 格式化为: 文件路径:行号:列号
                                allErrors.add("$filePath:$lineNumber:$column\n${message.message}")
                            } else {
                                // 如果无法获取精确位置，则只显示文件路径
                                allErrors.add("$filePath\n${message.message}")
                            }
                        }

                        // 累计错误数量和中止状态
                        totalErrorCount += errorCount
                        if (aborted) {
                            hasAborted = true
                        }

                        // 增加已完成的计数
                        completedCount++

                        // 如果所有文件都编译完成，调用回调函数
                        if (completedCount >= totalCount) {
                            val result = BuildResult(
                                success = !hasAborted && totalErrorCount == 0,
                                aborted = hasAborted,
                                errorCount = totalErrorCount,
                                errors = allErrors
                            )
                            callback(result)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            logger.error("检查文件编译错误时发生异常", e)
            val result = BuildResult(
                success = false,
                aborted = true,
                errorCount = 1,
                errors = listOf("检查编译错误异常: ${e.message}")
            )
            callback(result)
        }
    }

    fun checkFilesCompileErrors(filePaths: List<String>, callback: (BuildResult) -> Unit) {
        try {
            val compilerManager = CompilerManager.getInstance(project)
            ApplicationManager.getApplication().invokeLater {
                // 将相对路径转换为 VirtualFile 对象
                val virtualFiles = filePaths.mapNotNull { relativePath ->
                    val absolutePath = if (project.basePath != null) {
                        "${project.basePath}/$relativePath"
                    } else {
                        relativePath
                    }
                    LocalFileSystem.getInstance().findFileByPath(absolutePath)
                }.toTypedArray()

                if (virtualFiles.isEmpty()) {
                    val result = BuildResult(
                        success = false,
                        aborted = false,
                        errorCount = 1,
                        errors = listOf("未找到指定的文件: ${filePaths.joinToString(", ")}")
                    )
                    callback(result)
                    return@invokeLater
                }

                compilerManager.compile(virtualFiles) { aborted, errorCount, warningCount, context ->
                    // 收集当前文件的编译错误
                    val allErrors = mutableListOf<String>()
                    val errorMessages = context.getMessages(CompilerMessageCategory.ERROR)

                    errorMessages.forEach { message ->
                        val navigatable = message.navigatable
                        // 获取文件路径
                        val absolutePath = message.virtualFile?.path ?: "未知文件"

                        // 将绝对路径转换为相对于项目根目录的相对路径
                        val filePath = if (absolutePath != "未知文件" && project.basePath != null) {
                            val basePath = project.basePath!!
                            if (absolutePath.startsWith(basePath)) {
                                absolutePath.substring(basePath.length).removePrefix("/")
                            } else {
                                absolutePath
                            }
                        } else {
                            absolutePath
                        }

                        if (navigatable != null && navigatable is com.intellij.openapi.fileEditor.OpenFileDescriptor) {
                            // 获取行号和列号 (IntelliJ的行号是0基，需要+1转成1基)
                            val lineNumber = navigatable.line + 1
                            val column = navigatable.column + 1
                            // 格式化为: 文件路径:行号:列号
                            allErrors.add("$filePath:$lineNumber:$column\n${message.message}")
                        } else {
                            // 如果无法获取精确位置，则只显示文件路径
                            allErrors.add("$filePath\n${message.message}")
                        }
                    }

                    val result = BuildResult(
                        success = !aborted && errorCount == 0,
                        aborted = aborted,
                        errorCount = errorCount,
                        errors = allErrors
                    )
                    callback(result)
                }
            }
        } catch (e: Exception) {
            logger.error("检查文件编译错误时发生异常", e)
            val result = BuildResult(
                success = false,
                aborted = true,
                errorCount = 1,
                errors = listOf("检查编译错误异常: ${e.message}")
            )
            callback(result)
        }
    }


    /**
     * 构建项目（异步版本）- 使用增量编译
     *
     * @param callback 构建完成后的回调函数
     */
    fun buildProject(callback: (BuildResult) -> Unit) {
        try {
            val compilerManager = CompilerManager.getInstance(project)
            val errors = mutableListOf<String>()
//            val warnings = mutableListOf<String>()

            ApplicationManager.getApplication().invokeLater {
                // 在增量编译之前，先进行maven reload
                reloadMavenProject()

                // 使用 make 进行增量编译，而不是 rebuild 全量编译
                compilerManager.make { aborted, errorCount, warningCount, context ->
                    // 从编译上下文中收集错误和警告信息
                    // 分别获取错误和警告消息
                    val errorMessages = context.getMessages(CompilerMessageCategory.ERROR)
//                    val warningMessages = context.getMessages(CompilerMessageCategory.WARNING)

                    errorMessages.forEach { message ->
                        val navigatable = message.navigatable
                        // 获取文件路径
                        val absolutePath = message.virtualFile?.path ?: "未知文件"

                        // 将绝对路径转换为相对于项目根目录的相对路径
                        val filePath = if (absolutePath != "未知文件" && project.basePath != null) {
                            val basePath = project.basePath!!
                            if (absolutePath.startsWith(basePath)) {
                                absolutePath.substring(basePath.length).removePrefix("/")
                            } else {
                                absolutePath
                            }
                        } else {
                            absolutePath
                        }

                        if (navigatable != null && navigatable is com.intellij.openapi.fileEditor.OpenFileDescriptor) {
                            // 获取行号和列号 (IntelliJ的行号是0基，需要+1转成1基)
                            val lineNumber = navigatable.line + 1
                            val column = navigatable.column + 1
                            // 格式化为: 文件路径:行号:列号
                            errors.add("$filePath:$lineNumber:$column\n${message.message}")
                        } else {
                            // 如果无法获取精确位置，则只显示文件路径
                            errors.add("$filePath\n${message.message}")
                        }
                    }
//
//                    warningMessages.forEach { message ->
//                        warnings.add(message.message)
//                    }

                    val result = BuildResult(
                        success = !aborted && errorCount == 0,
                        aborted = aborted,
                        errorCount = errorCount,
//                        warningCount = warningCount,
                        errors = errors,
//                        warnings = warnings
                    )

                    callback(result)
                }
            }
        } catch (e: Exception) {
            logger.error("构建项目时发生异常", e)
            val result = BuildResult(
                success = false,
                aborted = true,
                errorCount = 1,
//                warningCount = 0,
                errors = listOf("构建异常: ${e.message}"),
//                warnings = emptyList()
            )
            callback(result)
        }
    }

    /**
     * 重新加载Maven项目
     */
    private fun reloadMavenProject() {
        try {
            // 检查项目是否包含Maven模块
            val mavenProjectsManager = org.jetbrains.idea.maven.project.MavenProjectsManager.getInstance(project)
            if (mavenProjectsManager != null && mavenProjectsManager.isMavenizedProject) {
                logger.info("开始重新加载Maven项目")

                // 强制重新导入所有Maven项目
                mavenProjectsManager.forceUpdateAllProjectsOrFindAllAvailablePomFiles()

                logger.info("Maven项目重新加载完成")
            } else {
                logger.debug("当前项目不是Maven项目，跳过Maven reload")
            }
        } catch (e: Exception) {
            logger.warn("Maven项目重新加载失败: ${e.message}", e)
        }
    }

    /**
     * 根据类名查找Java类的路径
     *
     * @param className 类名
     * @return 包含类路径和导入信息的Map，如果未找到则返回null
     * @deprecated 使用 findJavaClassAndContent 方法替代，该方法提供更完整的功能和更好的类型安全
     */
    @Deprecated("使用 findJavaClassAndContent 方法替代", ReplaceWith("findJavaClassAndContent(className)"))
    fun findJavaClass(className: String): Map<String, Any?>? {
        return DumbService.getInstance(project).runReadActionInSmartMode<Map<String, Any?>?> {
            try {
                val psiFacade = com.intellij.psi.JavaPsiFacade.getInstance(project)
                val searchScope = com.intellij.psi.search.GlobalSearchScope.allScope(project)

                // 先尝试直接查找完全匹配的类名
                var psiClass = psiFacade.findClass(className, searchScope)

                // 如果没有找到，尝试在项目所有类中搜索简单类名
                if (psiClass == null) {
                    val psiShortNamesCache = com.intellij.psi.search.PsiShortNamesCache.getInstance(project)
                    val classesWithName = psiShortNamesCache.getClassesByName(
                        className.substringAfterLast('.'),
                        searchScope
                    )


                    if (classesWithName.isNotEmpty()) {
                        psiClass = classesWithName.firstOrNull()
                    }
                }

                if (psiClass != null) {
                    val qualifiedName = psiClass.qualifiedName
                    val virtualFile = psiClass.containingFile.virtualFile
                    val absoluteFilePath = virtualFile?.path

                    // 将绝对路径转换为相对路径
                    val relativePath = if (absoluteFilePath != null && project.basePath != null) {
                        val basePath = project.basePath!!
                        if (absoluteFilePath.startsWith(basePath)) {
                            absoluteFilePath.substring(basePath.length).removePrefix("/")
                        } else {
                            absoluteFilePath
                        }
                    } else {
                        absoluteFilePath
                    }
                    return@runReadActionInSmartMode mapOf(
                        "qualifiedName" to qualifiedName,
                        "filePath" to relativePath,
                        "packageName" to psiClass.qualifiedName?.substringBeforeLast('.', ""),
                        "simpleClassName" to psiClass.name
                    )
                }

                null
            } catch (e: Exception) {
                logger.warn("Failed to find Java class: $className", e)
                null
            }
        }
    }

    /**
     * 单个类结果 - 当只找到一个匹配的类时返回
     */
    data class SingleClassResult(
        val qualifiedName: String,
        val filePath: String,
        val content: ClassFileContent?
    )

    /**
     * 多个类结果 - 当找到多个不同包名的类时返回
     */
    data class MultipleClassResult(
        val classes: List<ClassInfo>
    ) {
        data class ClassInfo(
            val qualifiedName: String,
            val filePath: String
        )
    }

    /**
     * 查找类和内容的结果封装
     */
    sealed class FindClassResult {
        data class Single(val result: SingleClassResult) : FindClassResult()
        data class Multiple(val result: MultipleClassResult) : FindClassResult()
    }

    /**
     * 根据传入的类名进行精确匹配搜索
     * 如果只找到一个类，返回包含 qualifiedName、filePath 和 content 的结果
     * 如果找到多个不同包名的类，返回包含每个类的 qualifiedName 和相对路径的结果
     *
     * @param className 要搜索的类名
     * @return FindClassResult 封装的搜索结果，如果没找到则返回null
     */
    fun findJavaClassAndContent(className: String): FindClassResult? {
        return DumbService.getInstance(project).runReadActionInSmartMode<FindClassResult?> {
            try {
                System.gc()
                val psiFacade = com.intellij.psi.JavaPsiFacade.getInstance(project)
                val searchScope = com.intellij.psi.search.GlobalSearchScope.allScope(project)

                // 使用 findClasses 方法获取所有匹配的类
                val foundClasses = mutableListOf<com.intellij.psi.PsiClass>()

                // 先尝试完全限定名查找
                if (className.contains('.')) {
                    val psiClass = psiFacade.findClass(className, searchScope)
                    if (psiClass != null) {
                        foundClasses.add(psiClass)
                    }
                } else {
                    // 如果是简单类名，使用 PsiShortNamesCache 搜索所有同名类
                    val psiShortNamesCache = com.intellij.psi.search.PsiShortNamesCache.getInstance(project)
                    val classesWithName = psiShortNamesCache.getClassesByName(className, searchScope)
                    foundClasses.addAll(classesWithName)
                }

                when (foundClasses.size) {
                    0 -> null
                    1 -> {
                        // 只找到一个类，返回详细信息包括内容
                        val singleClass = foundClasses.first()
                        val qualifiedName = singleClass.qualifiedName ?: return@runReadActionInSmartMode null
                        val virtualFile = singleClass.containingFile.virtualFile ?: return@runReadActionInSmartMode null
                        val absoluteFilePath = virtualFile.path

                        // 将绝对路径转换为相对路径
                        val relativePath = if (project.basePath != null) {
                            CodeBaseUtils.calculateRelativePath(project.basePath.toString(), absoluteFilePath)
                        } else {
                            absoluteFilePath
                        }

                        // 读取文件内容
                        val content = readFile(relativePath, null, null, virtualFile)
                        System.gc()
                        FindClassResult.Single(
                            SingleClassResult(
                                qualifiedName = qualifiedName,
                                filePath = relativePath,
                                content = content
                            )
                        )
                    }
                    else -> {
                        // 找到多个类，返回类信息列表
                        val classInfos = foundClasses.mapNotNull { psiClass ->
                            val qualifiedName = psiClass.qualifiedName
                            val virtualFile = psiClass.containingFile.virtualFile
                            val absoluteFilePath = virtualFile?.path

                            if (qualifiedName != null && absoluteFilePath != null) {
                                val relativePath = if (project.basePath != null) {
                                    CodeBaseUtils.calculateRelativePath(project.basePath.toString(), absoluteFilePath)
                                } else {
                                    absoluteFilePath
                                }
                                MultipleClassResult.ClassInfo(
                                    qualifiedName = qualifiedName,
                                    filePath = relativePath
                                )
                            } else {
                                null
                            }
                        }

                        System.gc()
                        if (classInfos.isNotEmpty()) {
                            FindClassResult.Multiple(MultipleClassResult(classInfos))
                        } else {
                            null
                        }
                    }
                }
            } catch (e: Exception) {
                logger.warn("Failed to find Java class and content: $className", e)
                null
            }
        }
    }

    fun unwrapFindClassResult(rawResult: CodeFileService.FindClassResult?): Any? {
        return when (rawResult) {
            is CodeFileService.FindClassResult.Single -> rawResult.result
            is CodeFileService.FindClassResult.Multiple -> rawResult.result
            else -> null
        }
    }

    /**
     * 解析 jar 包路径，处理相对路径和项目基础路径的合并
     *
     * @param jarPath jar 包路径，可能包含 ../ 等相对路径
     * @return 绝对路径
     */
    private fun resolveJarPathWithBasePath(jarPath: String): String {
        return try {
            val basePath = project.basePath
            if (basePath == null) {
                // 如果没有项目基础路径，使用默认的路径解析
                return resolveToAbsolutePath(jarPath)
            }

            // 使用 Java NIO 的 Path 来正确处理相对路径解析
            val basePathObj = java.nio.file.Paths.get(basePath)
            val resolvedPath = basePathObj.resolve(jarPath).normalize()
            resolvedPath.toString()
        } catch (e: Exception) {
            logger.warn("Failed to resolve jar path with base path: $jarPath", e)
            // 回退到原来的方法
            resolveToAbsolutePath(jarPath)
        }
    }

    /**
     * 从 jar 包路径获取 VirtualFile
     *
     * @param classFilePath jar 包路径，格式如：../../.m2/repository/com/toco/vs-elasticsearch/1.0.0-SNAPSHOT/vs-elasticsearch-1.0.0-20250713.084606-27.jar!//com/vs/es/query/VSQueryResult.class
     * @return VirtualFile 对象，如果无法获取则返回 null
     */
    private fun getJarVirtualFile(classFilePath: String): VirtualFile? {
        return try {
            // 分离 jar 文件路径和内部文件路径
            val parts = classFilePath.split(".jar!")
            if (parts.size != 2) {
                logger.warn("Invalid jar file path format: $classFilePath")
                return null
            }

            val jarPath = parts[0] + ".jar"
            val internalPath = parts[1].removePrefix("//").removePrefix("/")

            // jarPath可能超出project的目录范围，把jarPath和project.basePath做合并
            // basePath示例是 /Users/<USER>/IdeaProjects/ai_func_test28， jarPath示例是../../.m2/repository/com/toco/vs-elasticsearch/1.0.0-SNAPSHOT/vs-elasticsearch-1.0.0-20250713.084606-27.jar
            // 预期得到的结果是 /Users/<USER>/.m2/repository/com/toco/vs-elasticsearch/1.0.0-SNAPSHOT/vs-elasticsearch-1.0.0-20250713.084606-27.jar
            // 注意jarPath里的父路径..需要把basePath里的对应路径层级给替换掉
            val absoluteJarPath = resolveJarPathWithBasePath(jarPath)

            // 检查 jar 文件是否存在
            val jarFile = java.io.File(absoluteJarPath)
            if (!jarFile.exists()) {
                logger.warn("Jar file does not exist: $absoluteJarPath")
                return null
            }

            // 尝试多种方法获取 jar 包中的 VirtualFile
            var virtualFile: VirtualFile? = null

            // 方法1: 使用 jar:file:// 协议的 URL
            try {
                // jar URL 格式应该是: jar:file:///absolute/path/to/jar!/internal/path
                val jarUrl = "jar:file://$absoluteJarPath!/$internalPath"
                virtualFile = VfsUtil.findFileByURL(java.net.URL(jarUrl))
                if (virtualFile != null) {
                    logger.info("Successfully found virtual file using jar:file:// URL: $jarUrl")
                }
            } catch (e: Exception) {
                logger.warn("Failed to get virtual file using jar:file:// URL", e)
            }

            // 方法2: 如果方法1失败，尝试使用 JarFileSystem
            if (virtualFile == null) {
                try {
                    val jarFileSystem = JarFileSystem.getInstance()
                    val jarVirtualFile = LocalFileSystem.getInstance().findFileByPath(absoluteJarPath)
                    if (jarVirtualFile != null) {
                        val jarRoot = jarFileSystem.getJarRootForLocalFile(jarVirtualFile)
                        if (jarRoot != null) {
                            virtualFile = jarRoot.findFileByRelativePath(internalPath)
                            if (virtualFile != null) {
                                logger.info("Successfully found virtual file using JarFileSystem")
                            }
                        }
                    }
                } catch (e: Exception) {
                    logger.warn("Failed to get virtual file using JarFileSystem", e)
                }
            }

            if (virtualFile == null) {
                logger.warn("Could not find virtual file for jar path: $classFilePath")
            }

            virtualFile
        } catch (e: Exception) {
            logger.warn("Failed to get jar virtual file for path: $classFilePath", e)
            null
        }
    }

    /**
     * 读取 jar 包中文件的内容
     *
     * @param virtualFile jar 包中的虚拟文件
     * @return 文件内容，如果读取失败则返回 null
     */
    private fun readJarFileContent(virtualFile: VirtualFile): ClassFileContent? {
        return try {
            // 检查文件是否有效
            if (!virtualFile.isValid || virtualFile.isDirectory) {
                return null
            }

            // 检查是否是二进制文件（.class 文件）
            if (virtualFile.fileType.isBinary) {
                // 对于 .class 文件，尝试使用 IntelliJ 的反编译功能
                return ApplicationManager.getApplication().runReadAction<ClassFileContent?> {
                    try {
                        val psiManager = PsiManager.getInstance(project)
                        val psiFile = psiManager.findFile(virtualFile)

                        if (psiFile is ClsFileImpl) {
                            // 这是一个已编译的类文件，IntelliJ 会自动反编译
                            val decompiledText = psiFile.text
                            if (decompiledText.isNotEmpty()) {
                                return@runReadAction ClassFileContent(decompiledText)
                            }
                        }

                        // 如果反编译失败，回退到原来的处理方式
                        ClassFileContent("[JAR包中的编译文件，无法读取源码]")
                    } catch (e: Exception) {
                        logger.warn("Failed to decompile class file: ${virtualFile.path}", e)
                        ClassFileContent("[JAR包中的编译文件，反编译失败: ${e.message}]")
                    }
                }
            }

            // 对于源码文件（.java），直接读取内容
            val content = String(virtualFile.contentsToByteArray())
            ClassFileContent(content)
        } catch (e: Exception) {
            logger.warn("Failed to read jar file content: ${virtualFile.path}", e)
            null
        }
    }

    /**
     * 接受单个文件的变更，清除diff高亮
     *
     * @param filePath 要接受变更的文件路径
     * @return 操作结果，包含是否成功和相关信息
     */
    fun acceptChangesForSingleFile(filePath: String): AcceptChangesResult {
        val successFiles = mutableListOf<String>()
        val failedFiles = mutableListOf<String>()

        // 确保在EDT线程中执行UI相关操作
        if (ApplicationManager.getApplication().isDispatchThread) {
            // 如果已经在EDT线程，直接执行
            executeAcceptChangesForSingleFile(filePath, successFiles, failedFiles)
        } else {
            // 否则，在EDT线程上同步执行
            ApplicationManager.getApplication().invokeAndWait {
                executeAcceptChangesForSingleFile(filePath, successFiles, failedFiles)
            }
        }

        return AcceptChangesResult(
            success = failedFiles.isEmpty(),
            successCount = successFiles.size,
            failedFiles = failedFiles,
            successFiles = successFiles
        )
    }

    /**
     * 执行接受单个文件变更的实际操作（在EDT线程中调用）
     */
    private fun executeAcceptChangesForSingleFile(
        filePath: String,
        successFiles: MutableList<String>,
        failedFiles: MutableList<String>
    ) {
        try {
//            // 获取InlineDiffService实例
//            val inlineDiffService = project.service<InlineDiffService>()
//            // 清除该文件的所有内联diff高亮
//            inlineDiffService.clearInlineDiff(filePath)
//
//            // 从FileOriginalVersionService中清除原始版本记录
//            val originalVersionService = FileOriginalVersionService.getInstance(project)
//            originalVersionService.clearOriginalVersion(filePath)

            successFiles.add(filePath)
            logger.info("Successfully accepted changes for file: $filePath")
        } catch (e: Exception) {
            logger.warn("Failed to accept changes for file: $filePath", e)
            failedFiles.add(filePath)
        }
    }

    /**
     * 接受所有文件的变更，清除diff高亮
     *
     * @param filePaths 要接受变更的文件路径数组
     * @return 操作结果，包含成功处理的文件数量和失败的文件列表
     */
    fun acceptChangesForAllFiles(filePaths: Array<String>): AcceptChangesResult {
        val successFiles = mutableListOf<String>()
        val failedFiles = mutableListOf<String>()

        // 确保在EDT线程中执行UI相关操作
        if (ApplicationManager.getApplication().isDispatchThread) {
            // 如果已经在EDT线程，直接执行
            executeAcceptChangesForAllFiles(filePaths, successFiles, failedFiles)
        } else {
            // 否则，在EDT线程上同步执行
            ApplicationManager.getApplication().invokeAndWait {
                executeAcceptChangesForAllFiles(filePaths, successFiles, failedFiles)
            }
        }

        return AcceptChangesResult(
            success = failedFiles.isEmpty(),
            successCount = successFiles.size,
            failedFiles = failedFiles,
            successFiles = successFiles
        )
    }

    /**
     * 执行接受所有文件变更的实际操作（在EDT线程中调用）
     */
    private fun executeAcceptChangesForAllFiles(
        filePaths: Array<String>,
        successFiles: MutableList<String>,
        failedFiles: MutableList<String>
    ) {
        for (filePath in filePaths) {
            try {
//                // 获取InlineDiffService实例
//                val inlineDiffService = project.service<InlineDiffService>()
//                // 清除该文件的所有内联diff高亮
//                inlineDiffService.clearInlineDiff(filePath)

                // 从FileOriginalVersionService中清除原始版本记录
                val originalVersionService = FileOriginalVersionService.getInstance(project)
                originalVersionService.clearOriginalVersion(filePath)

                successFiles.add(filePath)
                logger.info("Successfully accepted changes for file: $filePath")
            } catch (e: Exception) {
                logger.warn("Failed to accept changes for file: $filePath", e)
                failedFiles.add(filePath)
            }
        }
    }

    /**
     * 丢弃单个文件的变更，恢复到原始版本
     *
     * @param filePath 要丢弃变更的文件路径
     * @return 操作结果，包含是否成功和相关信息
     */
    fun discardChangesForSingleFile(filePath: String): AcceptChangesResult {
        val successFiles = mutableListOf<String>()
        val failedFiles = mutableListOf<String>()

        // 确保在EDT线程中执行UI相关操作
        if (ApplicationManager.getApplication().isDispatchThread) {
            // 如果已经在EDT线程，直接执行
            executeDiscardChangesForSingleFile(filePath, successFiles, failedFiles)
        } else {
            // 否则，在EDT线程上同步执行
            ApplicationManager.getApplication().invokeAndWait {
                executeDiscardChangesForSingleFile(filePath, successFiles, failedFiles)
            }
        }

        return AcceptChangesResult(
            success = failedFiles.isEmpty(),
            successCount = successFiles.size,
            failedFiles = failedFiles,
            successFiles = successFiles
        )
    }

    /**
     * 删除单个文件的辅助方法
     */
    private fun deleteFile(filePath: String) {
        ApplicationManager.getApplication().runWriteAction {
            val virtualFile = FileUtil.getVirtualFile(filePath, project)
            if (virtualFile == null) {
                logger.warn("Virtual file not found: $filePath")
                return@runWriteAction
            }
            virtualFile.delete(this)
        }
    }

    /**
     * 执行丢弃单个文件变更的实际操作（在EDT线程中调用）
     */
    private fun executeDiscardChangesForSingleFile(
        filePath: String,
        successFiles: MutableList<String>,
        failedFiles: MutableList<String>
    ) {
        try {
            // 获取原始版本服务
            val originalVersionService = FileOriginalVersionService.getInstance(project)
            val originalContent = originalVersionService.getOriginalVersion(filePath)

            if (originalVersionService.isNewFile(filePath)) {
                // 文件是新建出来的，discard则直接删除文件
                logger.info("discardChangesForSingleFile: $filePath is new file, delete it")
                deleteFile(filePath)
                originalVersionService.clearOriginalVersion(filePath)
                return
            }

            if (originalContent == null) {
                logger.warn("No original version found for file: $filePath")
                failedFiles.add(filePath)
                return
            }

            // 获取虚拟文件
            val virtualFile = FileUtil.getVirtualFile(filePath, project)
            if (virtualFile == null) {
                logger.warn("Virtual file not found: $filePath")
                failedFiles.add(filePath)
                return
            }

            // 恢复文件内容到原始版本
            ApplicationManager.getApplication().runWriteAction {
                try {
                    virtualFile.setBinaryContent(originalContent.toByteArray(virtualFile.charset))

//                    // 获取InlineDiffService实例并清除内联diff高亮
//                    val inlineDiffService = project.service<InlineDiffService>()
//                    inlineDiffService.clearInlineDiff(filePath)

                    // 清除原始版本记录
                    originalVersionService.clearOriginalVersion(filePath)

                    successFiles.add(filePath)
                    logger.info("Successfully discarded changes for file: $filePath")
                } catch (e: IOException) {
                    logger.warn("Failed to write original content to file: $filePath", e)
                    failedFiles.add(filePath)
                }
            }
        } catch (e: Exception) {
            logger.warn("Failed to discard changes for file: $filePath", e)
            failedFiles.add(filePath)
        }
    }

    /**
     * 安全删除多个文件
     * 只能删除用户工作区内的文件，是安全的删除方式，用户可以撤销更改
     * 不会使用shell命令或其他可能不安全的方式删除文件
     *
     * @param filePaths 要删除的文件路径数组
     * @return 删除操作结果，包含成功和失败的文件列表
     */
    fun removeFiles(filePaths: Array<String>): RemoveFilesResult {
        if (filePaths.isEmpty()) {
            return RemoveFilesResult(
                success = true,
                message = "没有文件需要删除",
                totalCount = 0,
                successCount = 0
            )
        }

        val successFiles = mutableListOf<String>()
        val failedFiles = mutableListOf<String>()

        try {
            // 确保在EDT线程中执行UI相关操作
            if (ApplicationManager.getApplication().isDispatchThread) {
                // 如果已经在EDT线程，直接执行
                executeRemoveFiles(filePaths, successFiles, failedFiles)
            } else {
                // 否则，在EDT线程上同步执行
                ApplicationManager.getApplication().invokeAndWait {
                    executeRemoveFiles(filePaths, successFiles, failedFiles)
                }
            }
        } catch (e: ProcessCanceledException) {
            // 必须重新抛出 ProcessCanceledException
            throw e
        } catch (e: Exception) {
            logger.error("删除文件时发生异常", e)
            return RemoveFilesResult(
                success = false,
                message = "删除文件异常: ${e.message}",
                successFiles = successFiles,
                failedFiles = filePaths.toList(),
                totalCount = filePaths.size,
                successCount = successFiles.size
            )
        }

        val allSuccess = failedFiles.isEmpty()
        val message = if (allSuccess) {
            "成功删除 ${successFiles.size} 个文件"
        } else {
            "删除完成：成功 ${successFiles.size} 个，失败 ${failedFiles.size} 个"
        }

        return RemoveFilesResult(
            success = allSuccess,
            message = message,
            successFiles = successFiles,
            failedFiles = failedFiles,
            totalCount = filePaths.size,
            successCount = successFiles.size
        )
    }

    /**
     * 执行删除文件的实际操作（在EDT线程中调用）
     */
    private fun executeRemoveFiles(
        filePaths: Array<String>,
        successFiles: MutableList<String>,
        failedFiles: MutableList<String>
    ) {
        for (filePath in filePaths) {
            try {
                // 验证文件路径是否在工作区内
                if (!isFileInWorkspace(filePath)) {
                    logger.warn("文件不在工作区内，拒绝删除: $filePath")
                    failedFiles.add(filePath)
                    continue
                }

                // 执行安全删除
                val deleteResult = safeDeleteFile(filePath)
                if (deleteResult) {
                    successFiles.add(filePath)
                    logger.info("成功删除文件: $filePath")
                } else {
                    failedFiles.add(filePath)
                    logger.warn("删除文件失败: $filePath")
                }
            } catch (e: Exception) {
                logger.warn("删除文件时发生异常: $filePath", e)
                failedFiles.add(filePath)
            }
        }
    }

    /**
     * 检查文件是否在工作区内
     */
    private fun isFileInWorkspace(filePath: String): Boolean {
        return try {
            val absolutePath = resolveToAbsolutePath(filePath)
            val basePath = project.basePath

            if (basePath == null) {
                logger.warn("项目基础路径为空")
                return false
            }

            // 检查文件路径是否以项目基础路径开头
            val normalizedAbsolutePath = java.nio.file.Paths.get(absolutePath).normalize().toString()
            val normalizedBasePath = java.nio.file.Paths.get(basePath).normalize().toString()

            normalizedAbsolutePath.startsWith(normalizedBasePath)
        } catch (e: Exception) {
            logger.warn("检查文件是否在工作区内时发生异常: $filePath", e)
            false
        }
    }

    /**
     * 安全删除单个文件
     */
    private fun safeDeleteFile(filePath: String): Boolean {
        return try {
            ApplicationManager.getApplication().runWriteAction<Boolean> {
                val virtualFile = FileUtil.getVirtualFile(filePath, project)
                if (virtualFile == null) {
                    logger.warn("未找到虚拟文件: $filePath")
                    return@runWriteAction false
                }

                if (!virtualFile.isValid) {
                    logger.warn("虚拟文件无效: $filePath")
                    return@runWriteAction false
                }

                if (virtualFile.isDirectory) {
                    logger.warn("不能删除目录: $filePath")
                    return@runWriteAction false
                }

                // 使用 VirtualFile.delete() 进行安全删除
                // 这种方式支持 IntelliJ IDEA 的撤销功能
                virtualFile.delete(this)
                true
            }
        } catch (e: Exception) {
            logger.warn("安全删除文件失败: $filePath", e)
            false
        }
    }



    /**
     * 丢弃所有文件的变更，恢复到原始版本
     *
     * @param filePaths 要丢弃变更的文件路径数组
     * @return 操作结果，包含成功处理的文件数量和失败的文件列表
     */
    fun discardChangesForAllFiles(filePaths: Array<String>): AcceptChangesResult {
        val successFiles = mutableListOf<String>()
        val failedFiles = mutableListOf<String>()

        // 确保在EDT线程中执行UI相关操作
        if (ApplicationManager.getApplication().isDispatchThread) {
            // 如果已经在EDT线程，直接执行
            executeDiscardChangesForAllFiles(filePaths, successFiles, failedFiles)
        } else {
            // 否则，在EDT线程上同步执行
            ApplicationManager.getApplication().invokeAndWait {
                executeDiscardChangesForAllFiles(filePaths, successFiles, failedFiles)
            }
        }

        return AcceptChangesResult(
            success = failedFiles.isEmpty(),
            successCount = successFiles.size,
            failedFiles = failedFiles,
            successFiles = successFiles
        )
    }

    /**
     * 执行丢弃所有文件变更的实际操作（在EDT线程中调用）
     */
    private fun executeDiscardChangesForAllFiles(
        filePaths: Array<String>,
        successFiles: MutableList<String>,
        failedFiles: MutableList<String>
    ) {
        for (filePath in filePaths) {
            try {
                // 获取原始版本服务
                val originalVersionService = FileOriginalVersionService.getInstance(project)
                val originalContent = originalVersionService.getOriginalVersion(filePath)

                if (originalVersionService.isNewFile(filePath)) {
                    // 文件是新建出来的，discard则直接删除文件
                    logger.info("discardChangesForAllFiles: $filePath is new file, delete it")
                    deleteFile(filePath)
                    originalVersionService.clearOriginalVersion(filePath)
                    continue
                }

                if (originalContent == null) {
                    logger.warn("No original version found for file: $filePath")
                    failedFiles.add(filePath)
                    continue
                }

                // 获取虚拟文件
                val virtualFile = FileUtil.getVirtualFile(filePath, project)
                if (virtualFile == null) {
                    logger.warn("Virtual file not found: $filePath")
                    failedFiles.add(filePath)
                    continue
                }

                // 恢复文件内容到原始版本
                ApplicationManager.getApplication().runWriteAction {
                    try {
                        virtualFile.setBinaryContent(originalContent.toByteArray(virtualFile.charset))

//                        // 获取InlineDiffService实例并清除内联diff高亮
//                        val inlineDiffService = project.service<InlineDiffService>()
//                        inlineDiffService.clearInlineDiff(filePath)

                        // 清除原始版本记录
                        originalVersionService.clearOriginalVersion(filePath)

                        successFiles.add(filePath)
                        logger.info("Successfully discarded changes for file: $filePath")
                    } catch (e: IOException) {
                        logger.warn("Failed to write original content to file: $filePath", e)
                        failedFiles.add(filePath)
                    }
                }
            } catch (e: Exception) {
                logger.warn("Failed to discard changes for file: $filePath", e)
                failedFiles.add(filePath)
            }
        }
    }

    /**
     * 文件读取参数
     */
    data class FileReadRequest(
        val filePath: String,
        val startLine: Int? = null,
        val endLine: Int? = null
    )

    /**
     * 批量读取多个类文件的内容，并返回其基类内容
     *
     * @param fileRequests 文件读取请求列表，每个请求包含文件路径和可选的行号范围
     * @return 文件路径到类文件内容的映射，读取失败的文件对应值为null
     */
    fun readFiles(fileRequests: List<FileReadRequest>): Map<String, ClassFileContent?> {
        System.gc()
        val result = mutableMapOf<String, ClassFileContent?>()

        for (request in fileRequests) {
            try {
                val content = readFile(request.filePath, request.startLine, request.endLine)
                result[request.filePath] = content
            } catch (e: Exception) {
                logger.warn("Failed to read file: ${request.filePath}", e)
                result[request.filePath] = null
            }
        }
        System.gc()
        return result
    }

    /**
     * 异步批量读取多个类文件的内容
     *
     * @param fileRequests 文件读取请求列表
     * @param callback 回调函数，接收文件路径到内容的映射
     */
    fun readFilesAsync(
        fileRequests: List<FileReadRequest>,
        callback: (Map<String, ClassFileContent?>) -> Unit
    ) {
        BackgroundTaskUtil.executeInBackground(
            project = project,
            taskName = "Reading Class Files",
            action = {
                readFiles(fileRequests)
            },
            onSuccess = { result ->
                callback(result)
            },
            onFailure = { e ->
                logger.warn("Failed to read class files", e)
                callback(emptyMap())
            }
        )
    }



    /**
     * 处理获取已打开文件列表的请求
     * @return 包含已打开文件相对路径的列表
     */
    fun listOpenedFiles(): List<String> {
        return try {
            getOpenedFilesInRecentOrder()
        } catch (e: Exception) {
            logger.warn("Failed to get opened files list", e)
            emptyList()
        }
    }

    /**
     * 获取当前打开的文件列表
     * 简化实现，只返回打开的标签页，不关心顺序
     */
    private fun getOpenedFilesInRecentOrder(): List<String> {
        val projectBasePath = project.basePath

        return try {
            val fileEditorManager = FileEditorManager.getInstance(project)
            val openFiles = fileEditorManager.openFiles.toList()
            logger.debug("Found ${openFiles.size} opened files")
            convertToRelativePaths(openFiles, projectBasePath)
        } catch (e: Exception) {
            logger.warn("Failed to get opened files list: ${e.message}")
            emptyList()
        }
    }



    /**
     * 将文件列表转换为相对路径
     */
    private fun convertToRelativePaths(files: List<com.intellij.openapi.vfs.VirtualFile>, projectBasePath: String?): List<String> {
        return files.mapNotNull { virtualFile ->
            if (projectBasePath != null && virtualFile.path.startsWith(projectBasePath)) {
                // 计算相对路径
                val relativePath = virtualFile.path.substring(projectBasePath.length)
                // 移除开头的路径分隔符
                if (relativePath.startsWith("/") || relativePath.startsWith("\\")) {
                    relativePath.substring(1)
                } else {
                    relativePath
                }
            } else {
                // 如果无法计算相对路径，返回文件名
                virtualFile.name
            }
        }
    }

    /**
     * 打开指定文件的标签页
     * @param filePath 文件路径（可以是相对路径或绝对路径）
     * @return Map<String, Any> 操作结果
     */
    fun openFile(filePath: String): Map<String, Any> {
        return try {
            val projectBasePath = project.basePath
            val absolutePath = if (java.io.File(filePath).isAbsolute) {
                filePath
            } else {
                // 如果是相对路径，转换为绝对路径
                if (projectBasePath != null) {
                    java.io.File(projectBasePath, filePath).absolutePath
                } else {
                    filePath
                }
            }

            val virtualFile = LocalFileSystem.getInstance().findFileByPath(absolutePath)
            if (virtualFile == null) {
                mapOf(
                    "success" to false,
                    "message" to "文件不存在: $filePath"
                )
            } else if (!virtualFile.exists()) {
                mapOf(
                    "success" to false,
                    "message" to "文件不存在: $filePath"
                )
            } else {
                ApplicationManager.getApplication().invokeLater {
                    val fileEditorManager = FileEditorManager.getInstance(project)
                    fileEditorManager.openFile(virtualFile, true)
                }

                mapOf(
                    "success" to true,
                    "message" to "文件已打开: $filePath",
                    "filePath" to filePath
                )
            }
        } catch (e: Exception) {
            logger.warn("Failed to open file: $filePath", e)
            mapOf(
                "success" to false,
                "message" to "打开文件失败: ${e.message}"
            )
        }
    }

    companion object {
        /**
         * 获取服务实例
         */
        fun getInstance(project: Project): CodeFileService = project.service()
    }
}
